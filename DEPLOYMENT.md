# Deployment Guide for StokvelGrocery

This guide provides instructions for deploying the StokvelGrocery application without test files and dependencies.

## Deployment Options

### Option 1: Using Vercel (Recommended)

The project is configured to deploy on Vercel without test files and dependencies.

1. **Push your changes to GitHub**:
   ```bash
   git add .
   git commit -m "Your commit message"
   git push
   ```

2. **Deploy with Vercel CLI**:
   ```bash
   vercel
   ```

3. **Or deploy from the Vercel Dashboard**:
   - Go to [Vercel Dashboard](https://vercel.com/dashboard)
   - Import your repository
   - Configure the project settings
   - Deploy

### Option 2: Manual Deployment

1. **Prepare for deployment**:
   ```bash
   # For Linux/Mac
   ./deploy.sh

   # For Windows
   deploy.bat
   ```

2. **Check for test dependencies**:
   ```bash
   npm run check-deps
   ```

3. **Build the application**:
   ```bash
   npm run prepare-deploy
   ```

4. **Deploy the built application**:
   - The built application will be in the `.next` directory
   - Deploy this directory to your hosting provider

## Troubleshooting

### Dependency Conflicts

If you encounter dependency conflicts during deployment, try the following:

1. **Use the production package.json**:
   ```bash
   cp package.prod.json package.json
   ```

2. **Install only production dependencies**:
   ```bash
   npm install --production
   ```

3. **Build the application**:
   ```bash
   npm run build
   ```

### Test Files Not Excluded

If test files are not being excluded from the deployment, try the following:

1. **Manually remove test files**:
   ```bash
   # For Linux/Mac
   rm -rf __tests__ e2e test-results playwright-report playwright/.cache jest.config.js jest.setup.js playwright.config.ts TESTING.md tsconfig.test.json

   # For Windows
   rmdir /s /q __tests__ e2e test-results playwright-report playwright\.cache
   del jest.config.js jest.setup.js playwright.config.ts TESTING.md tsconfig.test.json
   ```

2. **Check the build output**:
   ```bash
   npm run build
   ```

3. **Verify that test files are not included in the build**:
   ```bash
   ls -la .next
   ```

## Configuration Files

The following configuration files are used to exclude test files from the deployment:

- **vercel.json**: Configures Vercel to use the custom build script and ignore test files
- **package.prod.json**: Contains only the dependencies needed for production
- **build.js**: Handles the build process and ensures that only production dependencies are installed
- **.babelrc**: Configures Babel to ignore test files in production
- **next.config.js**: Configures Next.js to exclude test files from the production build
- **.vercelignore**: Excludes test files from being uploaded to Vercel
- **.npmignore**: Excludes test files from being included in the NPM package
- **.dockerignore**: Excludes test files from being included in Docker images

## Environment Variables

The following environment variables are used to configure the deployment:

- **NODE_ENV**: Set to `production` for production builds
- **VERCEL**: Set to `1` when deploying on Vercel

## Continuous Integration

The project is configured to deploy automatically when changes are pushed to the following branches:

- **main**: The main branch
- **group_transfer_with_redux**: The feature branch for group transfer with Redux
