// lib/routes.ts
// Type-safe route definitions for the StokvelGrocery application

export const ROUTES = {
  // Public routes
  HOME: '/',
  STORE: '/store',
  ABOUT: '/aboutus',
  FAQ: '/faq',
  CONTACT: '/contact',
  
  // Auth routes
  LOGIN: '/login',
  REGISTER: '/register',
  
  // Profile routes
  PROFILE: '/profile',
  PROFILE_ORDERS: '/profile/orders',
  PROFILE_GROUPS: '/profile/groups',
  PROFILE_PAYMENTS: '/profile/payments',
  PROFILE_SETTINGS: '/profile/settings',
  PROFILE_REFERRALS: '/profile/referrals',
  PROFILE_WISHLIST: '/profile/wishlist',
  
  // Groups routes
  GROUPS: '/groups',
  
  // Admin routes
  ADMIN: '/admin',
  ADMIN_AI: '/admin/ai',
  ADMIN_AUTOMATION: '/admin/automation',
  ADMIN_ANALYTICS: '/admin/analytics',
  ADMIN_PERFORMANCE: '/admin/performance',
  ADMIN_REPORTS: '/admin/reports',
  ADMIN_PRODUCTS: '/admin/products',
  ADMIN_PRODUCT_CATEGORIES: '/admin/product-categories',
  ADMIN_PRODUCT_CATEGORY_ARCHIVE: '/admin/product-category-archive',
  ADMIN_PRODUCT_ARCHIVE: '/admin/product-archive',
  ADMIN_ORDERS: '/admin/orders',
  ADMIN_CUSTOMERS: '/admin/customers',
  ADMIN_LOCATIONS: '/admin/locations',
  ADMIN_GROUP_REQUESTS: '/admin/group-requests',
  ADMIN_USERS_ENHANCED: '/admin/users/enhanced',
  ADMIN_SETTINGS: '/admin/settings',
  ADMIN_HELP: '/admin/help',
  ADMIN_PROFILE: '/admin/profile',
} as const;

// Dynamic route builders
export const buildGroupRoute = (groupId: string) => `/group/${groupId}` as const;
export const buildGroupProductsRoute = (groupId: string) => `/group/${groupId}/products` as const;
export const buildGroupMembersRoute = (groupId: string) => `/group/${groupId}/members` as const;
export const buildGroupOrdersRoute = (groupId: string) => `/group/${groupId}/orders` as const;
export const buildGroupAnalyticsRoute = (groupId: string) => `/group/${groupId}/analytics` as const;
export const buildGroupSettingsRoute = (groupId: string) => `/group/${groupId}/settings` as const;
export const buildGroupTrackingRoute = (groupId: string) => `/group/${groupId}/tracking` as const;
export const buildGroupDeliveryRoute = (groupId: string) => `/group/${groupId}/delivery` as const;
export const buildGroupNewOrderRoute = (groupId: string) => `/group/${groupId}/new-order` as const;

// Type definitions for all possible routes
export type StaticRoute = typeof ROUTES[keyof typeof ROUTES];
export type DynamicGroupRoute = ReturnType<typeof buildGroupRoute | typeof buildGroupProductsRoute | typeof buildGroupMembersRoute | typeof buildGroupOrdersRoute | typeof buildGroupAnalyticsRoute | typeof buildGroupSettingsRoute | typeof buildGroupTrackingRoute | typeof buildGroupDeliveryRoute | typeof buildGroupNewOrderRoute>;
export type AppRoute = StaticRoute | DynamicGroupRoute;

// Navigation items with proper typing
export const NAV_ITEMS = [
  { name: 'Home', route: ROUTES.HOME },
  { name: 'Store', route: ROUTES.STORE },
  { name: 'Groups', route: ROUTES.GROUPS },
  { name: 'Aboutus', route: ROUTES.ABOUT },
  { name: 'FAQ', route: ROUTES.FAQ },
  { name: 'Contact', route: ROUTES.CONTACT },
] as const;

export const ADMIN_NAV_ITEMS = [
  { label: 'Dashboard', href: ROUTES.ADMIN },
  { label: 'Products', href: ROUTES.ADMIN_PRODUCTS },
  { label: 'Product Categories', href: ROUTES.ADMIN_PRODUCT_CATEGORIES },
  { label: 'Category Archives', href: ROUTES.ADMIN_PRODUCT_CATEGORY_ARCHIVE },
  { label: 'Product Archive', href: ROUTES.ADMIN_PRODUCT_ARCHIVE },
  { label: 'Orders', href: ROUTES.ADMIN_ORDERS },
  { label: 'Customers', href: ROUTES.ADMIN_CUSTOMERS },
  { label: 'Locations', href: ROUTES.ADMIN_LOCATIONS },
  { label: 'Group Requests', href: ROUTES.ADMIN_GROUP_REQUESTS },
] as const;

export const PROFILE_NAV_ITEMS = [
  { label: 'My Profile', href: ROUTES.PROFILE },
  { label: 'My Orders', href: ROUTES.PROFILE_ORDERS },
  { label: 'My Groups', href: ROUTES.PROFILE_GROUPS },
  { label: 'Payment Methods', href: ROUTES.PROFILE_PAYMENTS },
] as const;

// Helper function to get route from navigation item name
export const getRouteFromNavItem = (itemName: string): StaticRoute => {
  const item = NAV_ITEMS.find(nav => nav.name === itemName);
  return item ? item.route : ROUTES.HOME;
};

// Type guard to check if a string is a valid static route
export const isStaticRoute = (route: string): route is StaticRoute => {
  return Object.values(ROUTES).includes(route as StaticRoute);
};

// Type-safe router push function
export const pushRoute = (router: { push: (route: string) => void }, route: AppRoute) => {
  router.push(route);
};




