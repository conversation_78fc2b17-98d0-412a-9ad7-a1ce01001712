// lib/redux/features/groupRequests/groupRequestsApiSlice.ts

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { StokvelGroup } from '@/types/stokvelgroup';

// Group Request interfaces
export interface GroupRequest {
  _id: string;
  // User Information
  userId: string;
  userEmail: string;
  userName: string;
  userPhone?: string;
  
  // Location Hierarchy
  provinceId: string;
  provinceName: string;
  cityId: string;
  cityName: string;
  townshipId: string;
  townshipName: string;
  locationId: string;
  locationName: string;
  fullLocationPath: string;
  
  // Group Details
  requestedGroupName: string;
  groupDescription?: string;
  
  // Request Management
  status: 'pending' | 'approved' | 'rejected';
  requestDate: string;
  reviewDate?: string;
  reviewedBy?: string;
  reviewNotes?: string;
  
  // Auto-generated Group Info
  createdGroupId?: string;
  
  // Metadata
  createdAt: string;
  updatedAt: string;
  
  // Populated fields
  user?: {
    _id: string;
    name: string;
    email: string;
    phone?: string;
  };
  reviewer?: {
    _id: string;
    name: string;
    email: string;
  };
  createdGroup?: {
    _id: string;
    name: string;
  };
}

export interface CreateGroupRequestInput {
  userId?: string; // Optional for anonymous requests
  userEmail: string;
  userName: string;
  userPhone?: string;
  userPassword?: string; // For anonymous users to register during approval
  provinceId: string;
  provinceName: string;
  cityId: string;
  cityName: string;
  townshipId: string;
  townshipName: string;
  locationId: string;
  locationName: string;
  fullLocationPath: string;
  requestedGroupName: string;
  groupDescription?: string;
}

export interface UpdateGroupRequestInput {
  id: string;
  status?: 'pending' | 'approved' | 'rejected';
  reviewNotes?: string;
}

export interface ApproveGroupRequestInput {
  id: string;
  reviewNotes?: string;
}

export interface RejectGroupRequestInput {
  id: string;
  reviewNotes: string;
}

export interface GroupRequestsResponse {
  requests: GroupRequest[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

export interface GroupRequestResponse {
  request: GroupRequest;
}

export interface ApprovalResponse {
  success: boolean;
  message: string;
  request: GroupRequest;
  createdGroup?: StokvelGroup;
}

export interface GroupRequestsQueryParams {
  page?: number;
  limit?: number;
  status?: 'pending' | 'approved' | 'rejected';
  search?: string;
}

// Base query with authentication
const baseQuery = fetchBaseQuery({
  baseUrl: '/api/group-requests',
  credentials: 'include', // Include cookies in requests
  prepareHeaders: (headers) => {
    headers.set('Content-Type', 'application/json');
    return headers;
  },
});

export const groupRequestsApi = createApi({
  reducerPath: 'groupRequestsApi',
  baseQuery,
  tagTypes: ['GroupRequest', 'GroupRequestList', 'UserRequests'],
  endpoints: (builder) => ({
    // Get all group requests (admin) or user's requests
    getGroupRequests: builder.query<GroupRequestsResponse, GroupRequestsQueryParams>({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();
        
        if (params.page) searchParams.append('page', params.page.toString());
        if (params.limit) searchParams.append('limit', params.limit.toString());
        if (params.status) searchParams.append('status', params.status);
        if (params.search) searchParams.append('search', params.search);
        
        return `?${searchParams.toString()}`;
      },
      providesTags: (result) =>
        result
          ? [
              ...result.requests.map(({ _id }) => ({ type: 'GroupRequest' as const, id: _id })),
              { type: 'GroupRequestList', id: 'LIST' },
            ]
          : [{ type: 'GroupRequestList', id: 'LIST' }],
    }),

    // Get specific group request
    getGroupRequestById: builder.query<GroupRequestResponse, string>({
      query: (id) => `/${id}`,
      providesTags: (result, error, id) => [{ type: 'GroupRequest', id }],
    }),

    // Create new group request
    createGroupRequest: builder.mutation<{ success: boolean; message: string; request: GroupRequest }, CreateGroupRequestInput>({
      query: (data) => ({
        url: '',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [
        { type: 'GroupRequestList', id: 'LIST' },
        { type: 'UserRequests', id: 'LIST' },
      ],
    }),

    // Update group request (admin only)
    updateGroupRequest: builder.mutation<{ success: boolean; message: string; request: GroupRequest }, UpdateGroupRequestInput>({
      query: ({ id, ...data }) => ({
        url: `/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'GroupRequest', id },
        { type: 'GroupRequestList', id: 'LIST' },
      ],
    }),

    // Approve group request (admin only)
    approveGroupRequest: builder.mutation<ApprovalResponse, ApproveGroupRequestInput>({
      query: ({ id, reviewNotes }) => ({
        url: `/${id}/approve`,
        method: 'POST',
        body: { reviewNotes },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'GroupRequest', id },
        { type: 'GroupRequestList', id: 'LIST' },
        'Groups', // Invalidate groups list since a new group was created
      ],
    }),

    // Reject group request (admin only)
    rejectGroupRequest: builder.mutation<{ success: boolean; message: string; request: GroupRequest }, RejectGroupRequestInput>({
      query: ({ id, reviewNotes }) => ({
        url: `/${id}/reject`,
        method: 'POST',
        body: { reviewNotes },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'GroupRequest', id },
        { type: 'GroupRequestList', id: 'LIST' },
      ],
    }),

    // Delete group request (admin only)
    deleteGroupRequest: builder.mutation<{ success: boolean; message: string }, string>({
      query: (id) => ({
        url: `/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'GroupRequest', id },
        { type: 'GroupRequestList', id: 'LIST' },
      ],
    }),

    // Get user's group requests
    getUserGroupRequests: builder.query<GroupRequestsResponse, GroupRequestsQueryParams>({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();
        
        if (params.page) searchParams.append('page', params.page.toString());
        if (params.limit) searchParams.append('limit', params.limit.toString());
        if (params.status) searchParams.append('status', params.status);
        
        return `?${searchParams.toString()}`;
      },
      providesTags: (result) =>
        result
          ? [
              ...result.requests.map(({ _id }) => ({ type: 'GroupRequest' as const, id: _id })),
              { type: 'UserRequests', id: 'LIST' },
            ]
          : [{ type: 'UserRequests', id: 'LIST' }],
    }),

    // Get pending requests count (for admin dashboard)
    getPendingRequestsCount: builder.query<{ count: number }, void>({
      query: () => '?status=pending&limit=1',
      transformResponse: (response: GroupRequestsResponse) => ({
        count: response?.pagination?.total || 0
      }),
      providesTags: [{ type: 'GroupRequestList', id: 'PENDING_COUNT' }],
    }),
  }),
});

export const {
  useGetGroupRequestsQuery,
  useGetGroupRequestByIdQuery,
  useCreateGroupRequestMutation,
  useUpdateGroupRequestMutation,
  useApproveGroupRequestMutation,
  useRejectGroupRequestMutation,
  useDeleteGroupRequestMutation,
  useGetUserGroupRequestsQuery,
  useGetPendingRequestsCountQuery,
  
  // Lazy queries
  useLazyGetGroupRequestsQuery,
  useLazyGetGroupRequestByIdQuery,
  useLazyGetUserGroupRequestsQuery,
} = groupRequestsApi;
