# Group Sharing System Implementation

## Overview

The StockvelMarket group sharing system allows logged-in users to share their stockvel groups on social media platforms (WhatsApp, Facebook, Twitter, LinkedIn, Email) with proper referral tracking. When new users join through shared links, the original sharer gets referral rewards.

## Features Implemented

### 🔗 **Group Share API** (`/api/groups/share`)
- **Authentication**: Requires valid JWT token
- **Member Verification**: Only group members can share their groups
- **Platform Support**: WhatsApp, Facebook, Twitter, LinkedIn, Email, Copy Link
- **Referral Tracking**: Generates URLs with referral codes and UTM parameters
- **Reward System**: Awards 50 points for sharing

### 🎯 **Group Join via Share API** (`/api/groups/join-via-share`)
- **POST**: Join group with referral attribution
- **GET**: Fetch group info for share link preview
- **Referral Processing**: Tracks referrer and awards bonus points
- **Duplicate Prevention**: Handles already-joined scenarios gracefully

### 🎨 **GroupShareButton Component**
- **Reusable**: Can be added to any group page
- **Customizable**: Multiple variants, sizes, and styling options
- **Interactive Modal**: Rich sharing interface with platform options
- **Custom Messages**: Users can personalize their share messages
- **Real-time Feedback**: Toast notifications and loading states

### 🌐 **Group Share Landing Page** (`/groups/join/[groupId]`)
- **Public Access**: No authentication required for viewing
- **Group Information**: Displays group details, member count, location
- **Referrer Recognition**: Shows who invited the user
- **Benefits Showcase**: Highlights advantages of joining
- **Seamless Join Flow**: Handles login/signup if needed

## Implementation Details

### **API Endpoints**

#### Share Group
```typescript
POST /api/groups/share
{
  "groupId": "string",
  "platform": "whatsapp" | "facebook" | "twitter" | "linkedin" | "email" | "copy",
  "customMessage": "string (optional)"
}
```

#### Join Group via Share
```typescript
POST /api/groups/join-via-share
{
  "groupId": "string",
  "userId": "string",
  "referralCode": "string (optional)",
  "utmSource": "string (optional)",
  "utmMedium": "string (optional)",
  "utmCampaign": "string (optional)"
}
```

### **Share URL Format**
```
https://stockvelmarket.com/groups/join/{groupId}?ref={referralCode}&utm_source={platform}&utm_medium=group_share&utm_campaign=group_invitation
```

### **Platform-Specific Share URLs**

- **WhatsApp**: `https://wa.me/?text={encodedMessage}`
- **Facebook**: `https://www.facebook.com/sharer/sharer.php?u={shareUrl}&quote={message}`
- **Twitter**: `https://twitter.com/intent/tweet?text={encodedMessage}`
- **LinkedIn**: `https://www.linkedin.com/sharing/share-offsite/?url={shareUrl}`
- **Email**: `mailto:?subject={subject}&body={message}`

## Reward System

### **Points Allocation**
- **Group Share**: 50 points per share
- **Successful Referral**: 200 points when someone joins via your link
- **Welcome Bonus**: 100 points for new member joining via referral

### **Referral Tracking**
- Creates `Referral` model entry linking referrer and referee
- Tracks UTM parameters for analytics
- Awards points to both referrer and referee
- Prevents self-referrals and duplicate rewards

## Usage Examples

### **Adding Share Button to Group Page**
```tsx
import { GroupShareButton } from '@/components/groups/GroupShareButton';

<GroupShareButton
  groupId={group._id}
  groupName={group.name}
  groupDescription={group.description}
  memberCount={group.members.length}
  variant="outline"
  size="sm"
/>
```

### **Share URL Generation**
```typescript
const response = await fetch('/api/groups/share', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    groupId: 'group123',
    platform: 'whatsapp',
    customMessage: 'Join my amazing stockvel group!'
  })
});
```

## Integration Points

### **Existing Group Pages Updated**
- `/groups/[groupId]/page.tsx` - Main group page
- `/group/[groupId]/page.tsx` - Alternative group dashboard
- `EnhancedGroupDashboard.tsx` - Advanced group features

### **Referral System Integration**
- Extended `promotionService.ts` with group-specific actions
- Updated loyalty points system for group referrals
- Enhanced `types/promotions.ts` with group sharing interfaces

## Security & Validation

- **Authentication Required**: All share actions require valid JWT
- **Member Verification**: Only group members can share
- **Input Validation**: Validates group IDs, user IDs, and platform types
- **Rate Limiting**: Prevents spam sharing (handled by existing middleware)
- **Error Handling**: Comprehensive error responses and logging

## Mobile Responsiveness

- **Responsive Design**: Works on all screen sizes
- **Touch-Friendly**: Large buttons and touch targets
- **Mobile Share**: Native mobile sharing when available
- **Progressive Enhancement**: Graceful fallbacks for older browsers

## Analytics & Tracking

- **UTM Parameters**: Full campaign tracking
- **Conversion Metrics**: Track share-to-join conversion rates
- **Platform Performance**: Monitor which platforms perform best
- **Referral Analytics**: Detailed referral performance data

## Future Enhancements

- **QR Code Generation**: For offline sharing
- **Social Media Templates**: Platform-optimized content
- **A/B Testing**: Test different share messages
- **Gamification**: Leaderboards for top sharers
- **Advanced Analytics**: Detailed sharing performance dashboards

## Testing

The system includes comprehensive error handling and validation. Test scenarios:

1. **Valid Share**: Member shares group successfully
2. **Invalid Group**: Non-existent group ID
3. **Non-Member**: User not in group tries to share
4. **Join via Share**: New user joins through shared link
5. **Duplicate Join**: User already in group tries to join again
6. **Invalid Referral**: Invalid or expired referral code

## Deployment Notes

- Ensure `NEXT_PUBLIC_BASE_URL` environment variable is set
- Configure social media app credentials if needed
- Test share URLs in production environment
- Monitor referral tracking accuracy
- Set up analytics tracking for share performance

The group sharing system is now fully integrated and ready for production use!
