// scripts/migrate-coordinates.js
// Migration script to add coordinates to existing groups and locations

const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.local' });

// Import models
const StokvelGroup = require('../models/StokvelGroup').default;
const Location = require('../models/Location').default;
const Township = require('../models/Township').default;
const City = require('../models/City').default;
const Province = require('../models/Province').default;

// Mapbox configuration
const MAPBOX_TOKEN = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN;

// South African province coordinates (approximate centers)
const PROVINCE_COORDINATES = {
  'Gauteng': { latitude: -26.2041, longitude: 28.0473 },
  'Western Cape': { latitude: -33.9249, longitude: 18.4241 },
  'KwaZulu-Natal': { latitude: -29.8587, longitude: 31.0218 },
  'Eastern Cape': { latitude: -32.2968, longitude: 26.4194 },
  'Free State': { latitude: -29.1178, longitude: 26.2336 },
  'Limpopo': { latitude: -23.4013, longitude: 29.4179 },
  'Mpumalanga': { latitude: -25.5653, longitude: 30.5279 },
  'North West': { latitude: -25.8601, longitude: 25.6402 },
  'Northern Cape': { latitude: -29.0467, longitude: 21.8569 }
};

// City coordinates (major cities)
const CITY_COORDINATES = {
  'Johannesburg': { latitude: -26.2041, longitude: 28.0473 },
  'Cape Town': { latitude: -33.9249, longitude: 18.4241 },
  'Durban': { latitude: -29.8587, longitude: 31.0218 },
  'Pretoria': { latitude: -25.7479, longitude: 28.2293 },
  'Port Elizabeth': { latitude: -33.9608, longitude: 25.6022 },
  'Bloemfontein': { latitude: -29.0852, longitude: 26.1596 },
  'East London': { latitude: -33.0153, longitude: 27.9116 },
  'Pietermaritzburg': { latitude: -29.6094, longitude: 30.3781 },
  'Kimberley': { latitude: -28.7282, longitude: 24.7499 },
  'Polokwane': { latitude: -23.9045, longitude: 29.4689 }
};

// Township coordinates (approximate - these would need to be more precise in production)
const TOWNSHIP_COORDINATES = {
  'Soweto': { latitude: -26.2678, longitude: 27.8546 },
  'Alexandra': { latitude: -26.1017, longitude: 28.0989 },
  'Khayelitsha': { latitude: -34.0351, longitude: 18.6920 },
  'Mitchells Plain': { latitude: -34.0364, longitude: 18.6275 },
  'Umlazi': { latitude: -29.9689, longitude: 30.8830 },
  'KwaMashu': { latitude: -29.7906, longitude: 30.9994 },
  'Mamelodi': { latitude: -25.7308, longitude: 28.3540 },
  'Soshanguve': { latitude: -25.5392, longitude: 28.1123 }
};

async function connectToDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

async function geocodeLocation(locationName, fallbackCoords) {
  if (!MAPBOX_TOKEN) {
    console.log(`⚠️  No Mapbox token, using fallback coordinates for ${locationName}`);
    return fallbackCoords;
  }

  try {
    const response = await fetch(
      `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(locationName + ', South Africa')}.json?access_token=${MAPBOX_TOKEN}&country=ZA&limit=1`
    );
    
    if (!response.ok) {
      throw new Error(`Geocoding API error: ${response.status}`);
    }

    const data = await response.json();
    
    if (data.features && data.features.length > 0) {
      const [longitude, latitude] = data.features[0].center;
      console.log(`✅ Geocoded ${locationName}: ${latitude}, ${longitude}`);
      return { latitude, longitude };
    } else {
      console.log(`⚠️  No geocoding results for ${locationName}, using fallback`);
      return fallbackCoords;
    }
  } catch (error) {
    console.error(`❌ Error geocoding ${locationName}:`, error.message);
    return fallbackCoords;
  }
}

async function migrateLocations() {
  console.log('\n🗺️  Starting location coordinate migration...');
  
  try {
    // Get all locations without coordinates
    const locations = await Location.find({
      $or: [
        { coordinates: { $exists: false } },
        { 'coordinates.latitude': { $exists: false } },
        { 'coordinates.longitude': { $exists: false } }
      ]
    }).populate({
      path: 'townshipId',
      populate: {
        path: 'cityId',
        populate: {
          path: 'provinceId'
        }
      }
    });

    console.log(`📍 Found ${locations.length} locations without coordinates`);

    for (const location of locations) {
      const township = location.townshipId;
      const city = township?.cityId;
      const province = city?.provinceId;

      if (!township || !city || !province) {
        console.log(`⚠️  Skipping location ${location.name} - incomplete hierarchy`);
        continue;
      }

      // Try to get coordinates in order of specificity
      let coordinates = null;
      
      // 1. Try township-specific coordinates
      if (TOWNSHIP_COORDINATES[township.name]) {
        coordinates = TOWNSHIP_COORDINATES[township.name];
        console.log(`📍 Using township coordinates for ${location.name} in ${township.name}`);
      }
      // 2. Try city coordinates with small random offset
      else if (CITY_COORDINATES[city.name]) {
        const cityCoords = CITY_COORDINATES[city.name];
        coordinates = {
          latitude: cityCoords.latitude + (Math.random() - 0.5) * 0.1, // ±0.05 degrees (~5km)
          longitude: cityCoords.longitude + (Math.random() - 0.5) * 0.1
        };
        console.log(`🏙️  Using city coordinates with offset for ${location.name} in ${city.name}`);
      }
      // 3. Try province coordinates with larger random offset
      else if (PROVINCE_COORDINATES[province.name]) {
        const provinceCoords = PROVINCE_COORDINATES[province.name];
        coordinates = {
          latitude: provinceCoords.latitude + (Math.random() - 0.5) * 0.5, // ±0.25 degrees (~25km)
          longitude: provinceCoords.longitude + (Math.random() - 0.5) * 0.5
        };
        console.log(`🌍 Using province coordinates with offset for ${location.name} in ${province.name}`);
      }
      // 4. Try geocoding
      else {
        const searchQuery = `${location.name}, ${township.name}, ${city.name}, ${province.name}`;
        coordinates = await geocodeLocation(searchQuery, {
          latitude: -26.2041 + (Math.random() - 0.5) * 2, // Default to SA center with large offset
          longitude: 28.0473 + (Math.random() - 0.5) * 2
        });
      }

      if (coordinates) {
        await Location.findByIdAndUpdate(location._id, {
          coordinates: {
            latitude: coordinates.latitude,
            longitude: coordinates.longitude
          },
          address: `${location.name}, ${township.name}, ${city.name}, ${province.name}`
        });
        console.log(`✅ Updated location: ${location.name}`);
      }

      // Rate limiting for geocoding API
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log('✅ Location coordinate migration completed');
  } catch (error) {
    console.error('❌ Error migrating locations:', error);
  }
}

async function migrateGroups() {
  console.log('\n👥 Starting group coordinate migration...');
  
  try {
    // Get all groups without coordinates
    const groups = await StokvelGroup.find({
      $or: [
        { coordinates: { $exists: false } },
        { 'coordinates.latitude': { $exists: false } },
        { 'coordinates.longitude': { $exists: false } }
      ]
    }).populate({
      path: 'locationId',
      populate: {
        path: 'townshipId',
        populate: {
          path: 'cityId',
          populate: {
            path: 'provinceId'
          }
        }
      }
    });

    console.log(`👥 Found ${groups.length} groups without coordinates`);

    for (const group of groups) {
      const location = group.locationId;
      
      if (location && location.coordinates) {
        // Use location coordinates with small random offset for privacy
        const coordinates = {
          latitude: location.coordinates.latitude + (Math.random() - 0.5) * 0.01, // ±0.005 degrees (~500m)
          longitude: location.coordinates.longitude + (Math.random() - 0.5) * 0.01
        };

        await StokvelGroup.findByIdAndUpdate(group._id, {
          coordinates,
          address: location.address || location.name
        });

        console.log(`✅ Updated group: ${group.name}`);
      } else {
        console.log(`⚠️  Skipping group ${group.name} - no location coordinates available`);
      }
    }

    console.log('✅ Group coordinate migration completed');
  } catch (error) {
    console.error('❌ Error migrating groups:', error);
  }
}

async function main() {
  console.log('🚀 Starting coordinate migration script...');
  
  await connectToDatabase();
  
  try {
    await migrateLocations();
    await migrateGroups();
    
    console.log('\n🎉 Migration completed successfully!');
    console.log('\n📊 Summary:');
    
    const locationsWithCoords = await Location.countDocuments({
      'coordinates.latitude': { $exists: true },
      'coordinates.longitude': { $exists: true }
    });
    
    const groupsWithCoords = await StokvelGroup.countDocuments({
      'coordinates.latitude': { $exists: true },
      'coordinates.longitude': { $exists: true }
    });
    
    console.log(`📍 Locations with coordinates: ${locationsWithCoords}`);
    console.log(`👥 Groups with coordinates: ${groupsWithCoords}`);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('👋 Disconnected from MongoDB');
  }
}

// Run the migration
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, migrateLocations, migrateGroups };
