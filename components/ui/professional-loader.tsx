// components/ui/professional-loader.tsx

"use client";

import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { Loader2, Package, Users, ShoppingCart, BarChart3 } from "lucide-react";

interface ProfessionalLoaderProps {
  isLoading: boolean;
  type?: 'page' | 'component' | 'overlay';
  message?: string;
  className?: string;
}

interface PageTransitionLoaderProps {
  isLoading: boolean;
  fromPage?: string;
  toPage?: string;
}

interface ComponentLoaderProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  message?: string;
}

// Main Professional Loader
export function ProfessionalLoader({ 
  isLoading, 
  type = 'component', 
  message = "Loading...", 
  className 
}: ProfessionalLoaderProps) {
  if (type === 'page') {
    return (
      <AnimatePresence>
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className={cn(
              "fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-[#2A7C6C] to-[#1E5A4F]",
              className
            )}
          >
            <div className="text-center text-white">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="mx-auto mb-4"
              >
                <div className="relative">
                  <div className="w-16 h-16 border-4 border-white/20 rounded-full"></div>
                  <div className="absolute top-0 left-0 w-16 h-16 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
                </div>
              </motion.div>
              <motion.h2
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="text-xl font-semibold mb-2"
                style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
              >
                Stokvel Grocery
              </motion.h2>
              <motion.p
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="text-white/80"
              >
                {message}
              </motion.p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    );
  }

  if (type === 'overlay') {
    return (
      <AnimatePresence>
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className={cn(
              "absolute inset-0 z-40 flex items-center justify-center bg-white/80 backdrop-blur-sm",
              className
            )}
          >
            <div className="text-center">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
                className="mx-auto mb-3"
              >
                <Loader2 className="h-8 w-8 text-[#2A7C6C]" />
              </motion.div>
              <p className="text-sm text-gray-600">{message}</p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    );
  }

  // Component loader
  return (
    <AnimatePresence>
      {isLoading && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          className={cn("flex items-center justify-center p-4", className)}
        >
          <div className="text-center">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
              className="mx-auto mb-2"
            >
              <Loader2 className="h-6 w-6 text-[#2A7C6C]" />
            </motion.div>
            <p className="text-sm text-gray-600">{message}</p>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Page Transition Loader with context
export function PageTransitionLoader({ isLoading, fromPage, toPage }: PageTransitionLoaderProps) {
  const getPageIcon = (page?: string) => {
    if (!page) return Package;
    if (page.includes('products')) return Package;
    if (page.includes('members')) return Users;
    if (page.includes('cart')) return ShoppingCart;
    if (page.includes('progress')) return BarChart3;
    return Package;
  };

  const getPageName = (page?: string) => {
    if (!page) return "Loading";
    if (page.includes('products')) return "Products";
    if (page.includes('members')) return "Members";
    if (page.includes('cart')) return "Group Cart";
    if (page.includes('progress')) return "Progress";
    return "Loading";
  };

  const Icon = getPageIcon(toPage);
  const pageName = getPageName(toPage);

  return (
    <AnimatePresence>
      {isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-[#2A7C6C]/95 to-[#1E5A4F]/95 backdrop-blur-sm"
        >
          <div className="text-center text-white">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.1 }}
              className="mb-4"
            >
              <div className="relative mx-auto w-20 h-20 flex items-center justify-center">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                  className="absolute inset-0 border-4 border-white/20 rounded-full"
                />
                <motion.div
                  animate={{ rotate: -360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  className="absolute inset-2 border-2 border-white/40 rounded-full"
                />
                <Icon className="h-8 w-8 text-white z-10" />
              </div>
            </motion.div>
            
            <motion.h3
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-lg font-semibold mb-1"
            >
              Loading {pageName}
            </motion.h3>
            
            <motion.p
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="text-white/70 text-sm"
            >
              Please wait while we prepare your content
            </motion.p>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Component-specific loaders
export function ComponentLoader({ size = 'md', className, message }: ComponentLoaderProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6', 
    lg: 'h-8 w-8'
  };

  return (
    <div className={cn("flex items-center justify-center p-4", className)}>
      <div className="text-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
          className="mx-auto mb-2"
        >
          <Loader2 className={cn("text-[#2A7C6C]", sizeClasses[size])} />
        </motion.div>
        {message && <p className="text-sm text-gray-600">{message}</p>}
      </div>
    </div>
  );
}

// Skeleton loaders for specific components
export function ProductGridSkeleton() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: i * 0.1 }}
          className="bg-white rounded-lg border border-gray-200 overflow-hidden"
        >
          <div className="aspect-square bg-gray-200 animate-pulse" />
          <div className="p-4 space-y-3">
            <div className="h-4 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 bg-gray-200 rounded w-2/3 animate-pulse" />
            <div className="h-8 bg-gray-200 rounded animate-pulse" />
          </div>
        </motion.div>
      ))}
    </div>
  );
}

export function SearchSkeleton() {
  return (
    <div className="flex gap-3 mb-6">
      <div className="flex-1 h-10 bg-gray-200 rounded-lg animate-pulse" />
      <div className="h-10 w-24 bg-gray-200 rounded-lg animate-pulse" />
    </div>
  );
}
