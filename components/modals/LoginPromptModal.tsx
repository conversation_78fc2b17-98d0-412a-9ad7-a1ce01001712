"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { 
  LogIn, 
  Gift, 
  Star, 
  Users, 
  Share2,
  <PERSON>Right,
  Sparkles
} from "lucide-react";
import { motion } from "framer-motion";

interface LoginPromptModalProps {
  isOpen: boolean;
  onClose: () => void;
  action: string; // e.g., "share this group", "join this group", etc.
  returnUrl?: string;
  title?: string;
  description?: string;
}

export function LoginPromptModal({
  isOpen,
  onClose,
  action,
  returnUrl,
  title = "Login Required",
  description
}: LoginPromptModalProps) {
  const router = useRouter();
  const [isRedirecting, setIsRedirecting] = useState(false);

  const handleLoginRedirect = () => {
    setIsRedirecting(true);
    
    // Construct login URL with return URL if provided
    const loginUrl = returnUrl 
      ? `/login?returnUrl=${encodeURIComponent(returnUrl)}`
      : '/login';
    
    router.push(loginUrl);
  };

  const handleRegisterRedirect = () => {
    setIsRedirecting(true);
    router.push('/register');
  };

  const defaultDescription = `To ${action}, you need to be logged in to your StockvelMarket account. This ensures you earn rewards for your activities!`;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center flex items-center justify-center gap-2">
            <LogIn className="h-5 w-5 text-primary" />
            {title}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Icon and Message */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center space-y-4"
          >
            <div className="mx-auto w-16 h-16 bg-gradient-to-br from-primary to-purple-600 rounded-full flex items-center justify-center">
              <Share2 className="h-8 w-8 text-white" />
            </div>
            
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-gray-900">
                Join StockvelMarket to {action}
              </h3>
              <p className="text-sm text-gray-600">
                {description || defaultDescription}
              </p>
            </div>
          </motion.div>

          {/* Benefits */}
          <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4">
            <h4 className="font-semibold text-green-800 mb-3 flex items-center gap-2">
              <Gift className="h-4 w-4" />
              Why login?
            </h4>
            <ul className="space-y-2 text-sm text-green-700">
              <li className="flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-500" />
                Earn 50 points for sharing groups
              </li>
              <li className="flex items-center gap-2">
                <Sparkles className="h-4 w-4 text-purple-500" />
                Get 200 bonus points when someone joins via your link
              </li>
              <li className="flex items-center gap-2">
                <Users className="h-4 w-4 text-blue-500" />
                Build your referral network and earn rewards
              </li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button
              onClick={handleLoginRedirect}
              disabled={isRedirecting}
              className="w-full h-12 bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90 text-white font-semibold"
            >
              {isRedirecting ? (
                "Redirecting..."
              ) : (
                <>
                  <LogIn className="h-4 w-4 mr-2" />
                  Login to Continue
                  <ArrowRight className="h-4 w-4 ml-2" />
                </>
              )}
            </Button>

            <div className="text-center">
              <p className="text-sm text-gray-600 mb-2">
                Don't have an account?
              </p>
              <Button
                onClick={handleRegisterRedirect}
                disabled={isRedirecting}
                variant="outline"
                className="w-full"
              >
                Create Free Account
              </Button>
            </div>
          </div>

          {/* Close Option */}
          <div className="text-center">
            <Button
              onClick={onClose}
              variant="ghost"
              className="text-sm text-gray-500 hover:text-gray-700"
              disabled={isRedirecting}
            >
              Maybe later
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
