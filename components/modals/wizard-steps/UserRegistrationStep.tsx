// components/modals/wizard-steps/UserRegistrationStep.tsx

"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { User, Phone, Lock, CheckCircle, AlertCircle, ArrowRight, ArrowLeft, Mail, Shield } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { useAuthWithSignup } from "@/lib/redux/hooks/useAuthWithSignup";
import { useGetAllStokvelGroupsQuery, useJoinGroupMutation } from "@/lib/redux/features/groups/groupsApiSlice";
// Note: Group request functionality moved to ComprehensiveGroupRequestModal
import { useAddToCartMutation } from "@/lib/redux/features/cart/cartApiSlice";
import type { WizardData, WizardStep } from "../JoinGroupWizard";

interface UserRegistrationStepProps {
  wizardData: WizardData;
  updateWizardData: (data: Partial<WizardData>) => void;
  clearWizardData: () => void;
  goToStep: (step: WizardStep) => void;
  onSuccess?: (groupId?: string) => void;
  productId?: string;
  setIsLoading: (loading: boolean) => void;
}

export function UserRegistrationStep({
  wizardData,
  updateWizardData,
  clearWizardData,
  goToStep,
  setIsLoading,
  onSuccess,
  productId
}: UserRegistrationStepProps) {
  const { signup } = useAuthWithSignup();

  // Group joining mutations
  const [joinGroup] = useJoinGroupMutation();
  const [addToCart] = useAddToCartMutation();

  // Fetch all groups to get selected group details
  const { data: allGroups = [] } = useGetAllStokvelGroupsQuery();

  // Find the selected group
  const selectedGroup = wizardData.selectedGroupId
    ? allGroups.find(group => group._id === wizardData.selectedGroupId)
    : null;
  
  const [formData, setFormData] = useState({
    name: wizardData.name || "",
    phone: wizardData.phone || "",
    password: wizardData.password || "",
    confirmPassword: ""
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Validation functions
  const validateName = (name: string) => name.length >= 3;

  const validatePhone = (phone: string) => {
    // Remove leading/trailing spaces
    const cleanPhone = phone.trim();

    // Return false for empty phone
    if (!cleanPhone) return false;

    /*
    Global phone number validation supporting:

    Local formats (country-specific):
    - 0********* (10 digits starting with 0 - South Africa, UK, etc.)
    - 0712345678 (10 digits starting with 0)

    International formats:
    - +************ (South Africa)
    - ****** 123 4567 (USA/Canada)
    - +44 20 7946 0958 (UK)
    - +33 1 42 86 83 26 (France)
    - +49 30 12345678 (Germany)
    - +86 138 0013 8000 (China)
    - +91 98765 43210 (India)
    - +61 4 1234 5678 (Australia)
    - +81 90 1234 5678 (Japan)
    - +55 11 99999 9999 (Brazil)
    - +*********** 4567 (Nigeria)
    - +254 ********** (Kenya)
    - +265 ********* (Malawi)

    Flexible spacing and formatting
    */

    // Pattern 1: Local format (starts with 0, 7-15 digits total)
    // Covers most countries that use 0 prefix for national numbers
    const localPattern = /^0\d{6,14}$/;

    // Pattern 2: International format with country code
    // +[1-4 digit country code][space/no space][4-15 digits]
    // Supports all country codes (1-9999) and various number lengths
    const internationalPattern = /^\+\d{1,4}\s?\d{4,15}$/;

    // Pattern 3: International format with flexible spacing
    // Handles multiple spaces, dashes, dots, parentheses commonly used in phone formatting
    const internationalFlexiblePattern = /^\+\d{1,4}[\s\-\.\(\)]*\d[\d\s\-\.\(\)]{3,20}\d$/;

    // Pattern 4: US/Canada format variations
    // (*************, ************, ************, etc.
    const usFormatPattern = /^(\+1\s?)?(\(?\d{3}\)?[\s\-\.]?)?\d{3}[\s\-\.]?\d{4}$/;

    return localPattern.test(cleanPhone) ||
           internationalPattern.test(cleanPhone) ||
           internationalFlexiblePattern.test(cleanPhone) ||
           usFormatPattern.test(cleanPhone);
  };

  const validatePassword = (password: string) => password.length >= 6;
  const validateConfirmPassword = (password: string, confirmPassword: string) =>
    password === confirmPassword && validatePassword(password);

  // Helper function to normalize phone number for storage
  const normalizePhoneNumber = (phone: string) => {
    let cleanPhone = phone.trim();

    // If it's already international format, clean up formatting
    if (cleanPhone.startsWith('+')) {
      // Remove extra spaces, dashes, dots, parentheses and normalize
      cleanPhone = cleanPhone.replace(/[\s\-\.\(\)]+/g, ' ');
      // Ensure single space after country code
      cleanPhone = cleanPhone.replace(/(\+\d{1,4})\s+/, '$1 ');
      // Remove any trailing spaces
      cleanPhone = cleanPhone.trim();
      return cleanPhone;
    }

    // If it's local format starting with 0, try to convert to international
    // Default to South African (+27) for StockvelMarket, but keep original if uncertain
    if (cleanPhone.startsWith('0') && cleanPhone.length === 10) {
      // This is likely a South African number for StockvelMarket
      return `+27 ${cleanPhone.substring(1)}`;
    }

    // For other local formats or uncertain cases, keep as-is
    // The backend can handle country-specific normalization
    return cleanPhone;
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!validateName(formData.name)) {
      newErrors.name = "Name must be at least 3 characters";
    }

    if (!validatePhone(formData.phone)) {
      newErrors.phone = "Please enter a valid phone number (e.g., 0712345678 or +27 712345678)";
    }

    if (!validatePassword(formData.password)) {
      newErrors.password = "Password must be at least 6 characters";
    }

    if (!validateConfirmPassword(formData.password, formData.confirmPassword)) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    setIsLoading(true);

    try {
      console.log("🚀 Starting user registration process...");

      // STEP 1: User Registration
      const normalizedPhone = normalizePhoneNumber(formData.phone);
      const newUser = await signup(
        formData.name,
        wizardData.email,
        normalizedPhone,
        formData.password
      );

      console.log("✅ User registration successful:", newUser._id);

      // Update wizard data with user info
      updateWizardData({
        name: formData.name,
        phone: normalizedPhone,
        password: formData.password,
        userByEmailData: newUser,
        isUserKnown: true
      });

      // Note: Group request functionality moved to ComprehensiveGroupRequestModal
      // Users should use the standalone modal for group requests

      // STEP 3: Handle Pre-selected Group Join (if no group request)
      if (wizardData.selectedGroupId && newUser._id) {
        console.log("🔗 Auto-joining user to pre-selected group...");

        try {
          const joinResult = await joinGroup({
            userId: newUser._id,
            groupId: wizardData.selectedGroupId,
            isRelocation: false
          }).unwrap();

          if (joinResult.success) {
            // Add product to cart if provided
            if (productId) {
              console.log("🛒 Adding product to cart...");
              try {
                await addToCart({
                  userId: newUser._id,
                  productId,
                  quantity: 1,
                  groupId: wizardData.selectedGroupId
                }).unwrap();
                console.log("✅ Product added to cart successfully");
              } catch (cartError) {
                console.error("⚠️ Error adding product to cart:", cartError);
                // Don't fail the entire process if cart addition fails
              }
            }

            // Success - clear wizard data and close modal
            console.log("✅ Registration and group joining completed successfully");
            clearWizardData();
            setTimeout(() => {
              onSuccess?.(wizardData.selectedGroupId);
            }, 500);
          } else {
            console.error("❌ Group join failed:", joinResult);
            setErrors({ submit: "Registration successful, but failed to join group. Please try again." });
          }
        } catch (joinError) {
          console.error("❌ Error joining group:", joinError);
          setErrors({ submit: "Registration successful, but failed to join group. Please try again." });
        }
      } else {
        // STEP 4: No pre-selected group and no group request - go to location selection
        console.log("📍 No pre-selected group or group request, proceeding to location selection...");
        goToStep("location-selection");
      }
    } catch (error) {
      console.error("❌ Registration error:", error);
      setErrors({ submit: "Registration failed. Please try again." });
    } finally {
      setIsSubmitting(false);
      setIsLoading(false);
    }
  };

  const getFieldStatus = (field: string, validator: (value: string) => boolean) => {
    const value = formData[field as keyof typeof formData];
    if (!value) return "default";
    if (errors[field]) return "error";
    return validator(value) ? "success" : "error";
  };

  const getStatusIcon = (field: string, validator: (value: string) => boolean) => {
    const status = getFieldStatus(field, validator);
    switch (status) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const isFormValid = 
    validateName(formData.name) &&
    validatePhone(formData.phone) &&
    validatePassword(formData.password) &&
    validateConfirmPassword(formData.password, formData.confirmPassword);

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center space-y-2"
      >
        <div className="mx-auto w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mb-4">
          <User className="h-8 w-8 text-white" />
        </div>
        <h3 
          className="text-xl font-semibold text-gray-800"
          style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
        >
          Create your account
        </h3>
        <p 
          className="text-gray-600"
          style={{ fontFamily: "Avenir, sans-serif" }}
        >
          Join thousands of South Africans saving together
        </p>
      </motion.div>

      {/* Verified Email Display */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="p-4 bg-green-50 border border-green-200 rounded-xl"
      >
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
            <Shield className="h-4 w-4 text-white" />
          </div>
          <div className="flex-1">
            <p className="text-sm font-medium text-green-800">Verified Email Address</p>
            <p className="text-sm text-green-600 flex items-center gap-2">
              <Mail className="h-3 w-3" />
              {wizardData.email}
            </p>
          </div>
          <CheckCircle className="h-5 w-5 text-green-500" />
        </div>
      </motion.div>

      {/* Selected Group Display */}
      {wizardData.selectedGroupId && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.15 }}
          className="p-4 bg-emerald-50 border border-emerald-200 rounded-xl"
        >
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
              <User className="h-4 w-4 text-white" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-emerald-800">Selected Stokvel Group</p>
              <p className="text-sm text-emerald-600 font-medium">
                {selectedGroup ? selectedGroup.name : 'Loading group details...'}
              </p>
              {selectedGroup && (
                <p className="text-xs text-emerald-500 mt-1">
                  📍 {selectedGroup.geolocation} • {selectedGroup.members?.length || 0} members
                </p>
              )}
            </div>
            <CheckCircle className="h-5 w-5 text-emerald-500" />
          </div>
        </motion.div>
      )}

      {/* Debug Info - Show wizard data for troubleshooting */}
      {process.env.NODE_ENV === 'development' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="p-3 bg-gray-50 border border-gray-200 rounded-lg text-xs"
        >
          <details>
            <summary className="cursor-pointer font-medium text-gray-700">Debug: Wizard Data</summary>
            <pre className="mt-2 text-gray-600 overflow-auto">
              {JSON.stringify({
                email: wizardData.email,
                selectedGroupId: wizardData.selectedGroupId,
                selectedProvinceId: wizardData.selectedProvinceId,
                selectedCityId: wizardData.selectedCityId,
                selectedTownshipId: wizardData.selectedTownshipId,
                selectedLocationId: wizardData.selectedLocationId,
                isUserKnown: wizardData.isUserKnown,
                selectedGroupName: selectedGroup?.name
              }, null, 2)}
            </pre>
          </details>
        </motion.div>
      )}

      {/* Form Fields */}
      <div className="space-y-4">
        {/* Name Field */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-2"
        >
          <Label htmlFor="name" className="text-sm font-medium text-gray-700">
            Full Name
          </Label>
          <div className="relative">
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
              <User className="h-4 w-4 text-gray-400" />
            </div>
            <Input
              id="name"
              placeholder="Enter your full name"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              className={`pl-10 pr-10 h-12 ${
                getFieldStatus("name", validateName) === "success" 
                  ? "border-green-500 focus-visible:ring-green-500" 
                  : getFieldStatus("name", validateName) === "error"
                  ? "border-red-500 focus-visible:ring-red-500"
                  : "border-gray-300"
              }`}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {getStatusIcon("name", validateName)}
            </div>
          </div>
          {errors.name && (
            <p className="text-sm text-red-600">{errors.name}</p>
          )}
        </motion.div>

        {/* Phone Field */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
          className="space-y-2"
        >
          <Label htmlFor="phone" className="text-sm font-medium text-gray-700">
            Phone Number
          </Label>
          <div className="relative">
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
              <Phone className="h-4 w-4 text-gray-400" />
            </div>
            <Input
              id="phone"
              placeholder="e.g. 0712345678 or +27 712345678"
              value={formData.phone}
              onChange={(e) => handleInputChange("phone", e.target.value)}
              className={`pl-10 pr-10 h-12 ${
                getFieldStatus("phone", validatePhone) === "success" 
                  ? "border-green-500 focus-visible:ring-green-500" 
                  : getFieldStatus("phone", validatePhone) === "error"
                  ? "border-red-500 focus-visible:ring-red-500"
                  : "border-gray-300"
              }`}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {getStatusIcon("phone", validatePhone)}
            </div>
          </div>
          {errors.phone && (
            <p className="text-sm text-red-600">{errors.phone}</p>
          )}
          {!errors.phone && formData.phone && (
            <p className="text-xs text-gray-500">
              ✓ Supports global formats: local (0712345678) or international (+27 712345678)
            </p>
          )}
        </motion.div>

        {/* Password Field */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
          className="space-y-2"
        >
          <Label htmlFor="password" className="text-sm font-medium text-gray-700">
            Password
          </Label>
          <div className="relative">
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
              <Lock className="h-4 w-4 text-gray-400" />
            </div>
            <Input
              id="password"
              type="password"
              placeholder="Create a secure password"
              value={formData.password}
              onChange={(e) => handleInputChange("password", e.target.value)}
              className={`pl-10 pr-10 h-12 ${
                getFieldStatus("password", validatePassword) === "success" 
                  ? "border-green-500 focus-visible:ring-green-500" 
                  : getFieldStatus("password", validatePassword) === "error"
                  ? "border-red-500 focus-visible:ring-red-500"
                  : "border-gray-300"
              }`}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {getStatusIcon("password", validatePassword)}
            </div>
          </div>
          {errors.password && (
            <p className="text-sm text-red-600">{errors.password}</p>
          )}
        </motion.div>

        {/* Confirm Password Field */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
          className="space-y-2"
        >
          <Label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">
            Confirm Password
          </Label>
          <div className="relative">
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
              <Lock className="h-4 w-4 text-gray-400" />
            </div>
            <Input
              id="confirmPassword"
              type="password"
              placeholder="Confirm your password"
              value={formData.confirmPassword}
              onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
              className={`pl-10 pr-10 h-12 ${
                getFieldStatus("confirmPassword", (val) => validateConfirmPassword(formData.password, val)) === "success" 
                  ? "border-green-500 focus-visible:ring-green-500" 
                  : getFieldStatus("confirmPassword", (val) => validateConfirmPassword(formData.password, val)) === "error"
                  ? "border-red-500 focus-visible:ring-red-500"
                  : "border-gray-300"
              }`}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {getStatusIcon("confirmPassword", (val) => validateConfirmPassword(formData.password, val))}
            </div>
          </div>
          {errors.confirmPassword && (
            <p className="text-sm text-red-600">{errors.confirmPassword}</p>
          )}
        </motion.div>
      </div>

      {/* Submit Error */}
      {errors.submit && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="p-4 bg-red-50 border border-red-200 rounded-xl"
        >
          <p className="text-sm text-red-700">{errors.submit}</p>
        </motion.div>
      )}

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="flex gap-3 pt-4"
      >
        <Button
          variant="outline"
          onClick={() => goToStep("group-selection")}
          className="flex-1 h-12 border-gray-300 hover:bg-gray-50"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Group Selection
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={!isFormValid || isSubmitting}
          className="flex-1 h-12 bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50"
        >
          {isSubmitting ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              Creating Account...
            </div>
          ) : (
            <span className="flex items-center gap-2">
              Create Account
              <ArrowRight className="h-4 w-4" />
            </span>
          )}
        </Button>
      </motion.div>
    </div>
  );
}
