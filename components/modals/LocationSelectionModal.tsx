// components/modals/LocationSelectionModal.tsx

"use client";

import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { createPortal } from "react-dom";
import {
  X,
  ChevronLeft,
  Search,
  MapPin,
  Navigation,
  Check,
  ArrowRight,
  Loader2,
  Plus,
  Users,
  AlertCircle,
  ChevronDown,
  ChevronUp
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { cn } from "@/lib/utils";
import { useLocations } from "@/lib/redux/hooks/useLocations";
import type { Province, City, Township, Location } from "@/types/locations";
import type { StokvelGroup } from "@/types/stokvelgroup";
import { MapLocationSelector } from "@/components/maps/MapLocationSelector";

export interface LocationSelectionResult {
  provinceId: string;
  provinceName: string;
  cityId: string;
  cityName: string;
  townshipId: string;
  townshipName: string;
  locationId: string;
  locationName: string;
  fullPath: string;
}

export interface GroupRequestData {
  requestedGroupName: string;
  groupDescription?: string;
}

export interface GroupRequestSaveData extends GroupRequestData {
  locationSelection: LocationSelectionResult;
}

interface LocationSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLocationSelect: (result: LocationSelectionResult) => void;
  onGroupRequest?: (data: GroupRequestData & { locationSelection: LocationSelectionResult }) => Promise<void>;
  onGroupRequestSave?: (data: GroupRequestSaveData) => void;
  initialSelection?: Partial<LocationSelectionResult>;
  title?: string;
  userEmail?: string;
  userName?: string;
  userPhone?: string;
}

type SelectionLevel = "province" | "city" | "township" | "location" | "map";

const modalVariants = {
  hidden: { 
    opacity: 0,
    scale: 0.95,
    y: 20
  },
  visible: { 
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: "spring",
      damping: 25,
      stiffness: 300,
      duration: 0.4
    }
  },
  exit: { 
    opacity: 0,
    scale: 0.95,
    y: 20,
    transition: {
      duration: 0.2
    }
  }
};

const mobileModalVariants = {
  hidden: { 
    opacity: 0,
    y: "100%"
  },
  visible: { 
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      damping: 25,
      stiffness: 300,
      duration: 0.4
    }
  },
  exit: { 
    opacity: 0,
    y: "100%",
    transition: {
      duration: 0.3
    }
  }
};

const levelTransition = {
  initial: { opacity: 0, x: 20 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: -20 },
  transition: { duration: 0.2 }
};

export function LocationSelectionModal({
  isOpen,
  onClose,
  onLocationSelect,
  onGroupRequest,
  onGroupRequestSave,
  initialSelection,
  title = "Select Your Location",
  userEmail,
  userName,
  userPhone
}: LocationSelectionModalProps) {
  const [mounted, setMounted] = useState(false);
  const [currentLevel, setCurrentLevel] = useState<SelectionLevel>("province");
  const [searchQuery, setSearchQuery] = useState("");
  const [isMobile, setIsMobile] = useState(false);

  // Location selection hooks
  const {
    selectionData,
    handleProvinceChange,
    handleCityChange,
    handleTownshipChange,
    handleLocationChange,
    isSelectionComplete,
  } = useLocations();

  // Check if mobile on mount and handle body scroll
  useEffect(() => {
    setMounted(true);
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    checkMobile();
    window.addEventListener('resize', checkMobile);

    // Prevent body scroll when modal is open
    if (isOpen) {
      document.body.style.overflow = 'hidden';
      // Add modal attribute for detection
      document.body.setAttribute('data-modal-open', 'true');
    }

    return () => {
      window.removeEventListener('resize', checkMobile);
      document.body.style.overflow = '';
      document.body.removeAttribute('data-modal-open');
    };
  }, [isOpen]);

  // Initialize with existing selection
  useEffect(() => {
    if (initialSelection && isOpen) {
      if (initialSelection.provinceId) {
        handleProvinceChange(initialSelection.provinceId);
        if (initialSelection.cityId) {
          handleCityChange(initialSelection.cityId);
          if (initialSelection.townshipId) {
            handleTownshipChange(initialSelection.townshipId);
            if (initialSelection.locationId) {
              handleLocationChange(initialSelection.locationId);
              setCurrentLevel("location");
            } else {
              setCurrentLevel("location");
            }
          } else {
            setCurrentLevel("township");
          }
        } else {
          setCurrentLevel("city");
        }
      }
    }
  }, [initialSelection, isOpen, handleProvinceChange, handleCityChange, handleTownshipChange, handleLocationChange]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setCurrentLevel("province");
      setSearchQuery("");
    }
  }, [isOpen]);

  const handleBack = useCallback(() => {
    switch (currentLevel) {
      case "city":
        setCurrentLevel("province");
        break;
      case "township":
        setCurrentLevel("city");
        break;
      case "location":
        setCurrentLevel("township");
        break;
    }
    setSearchQuery("");
  }, [currentLevel]);

  const handleProvinceSelect = useCallback(async (province: Province) => {
    await handleProvinceChange(province._id);
    setCurrentLevel("city");
    setSearchQuery("");
  }, [handleProvinceChange]);

  const handleCitySelect = useCallback(async (city: City) => {
    await handleCityChange(city._id);
    setCurrentLevel("township");
    setSearchQuery("");
  }, [handleCityChange]);

  const handleTownshipSelect = useCallback(async (township: Township) => {
    await handleTownshipChange(township._id);
    setCurrentLevel("map");
    setSearchQuery("");
  }, [handleTownshipChange]);

  const handleLocationSelect = useCallback((location: Location) => {
    handleLocationChange(location._id);

    // Build the result object
    const selectedProvince = selectionData.availableProvinces.find(p => p._id === selectionData.selectedProvinceId);
    const selectedCity = selectionData.availableCities.find(c => c._id === selectionData.selectedCityId);
    const selectedTownship = selectionData.availableTownships.find(t => t._id === selectionData.selectedTownshipId);

    if (selectedProvince && selectedCity && selectedTownship) {
      const result: LocationSelectionResult = {
        provinceId: selectedProvince._id,
        provinceName: selectedProvince.name,
        cityId: selectedCity._id,
        cityName: selectedCity.name,
        townshipId: selectedTownship._id,
        townshipName: selectedTownship.name,
        locationId: location._id,
        locationName: location.name,
        fullPath: `${location.name}, ${selectedTownship.name}, ${selectedCity.name}, ${selectedProvince.name}`
      };

      onLocationSelect(result);
      onClose();
    }
  }, [handleLocationChange, selectionData, onLocationSelect, onClose]);

  // Handle group selection from map
  const handleMapGroupSelect = useCallback((group: StokvelGroup) => {
    // Build the result object using the group's location
    const selectedProvince = selectionData.availableProvinces.find(p => p._id === selectionData.selectedProvinceId);
    const selectedCity = selectionData.availableCities.find(c => c._id === selectionData.selectedCityId);
    const selectedTownship = selectionData.availableTownships.find(t => t._id === selectionData.selectedTownshipId);

    if (selectedProvince && selectedCity && selectedTownship) {
      const result: LocationSelectionResult = {
        provinceId: selectedProvince._id,
        provinceName: selectedProvince.name,
        cityId: selectedCity._id,
        cityName: selectedCity.name,
        townshipId: selectedTownship._id,
        townshipName: selectedTownship.name,
        locationId: group.locationId || "",
        locationName: group.address || group.name,
        fullPath: `${group.address || group.name}, ${selectedTownship.name}, ${selectedCity.name}, ${selectedProvince.name}`
      };

      onLocationSelect(result);
      onClose();
    }
  }, [selectionData, onLocationSelect, onClose]);

  // Handle new group request from map
  const handleMapNewGroupRequest = useCallback(async (coordinates: { latitude: number; longitude: number }, address: string) => {
    const selectedProvince = selectionData.availableProvinces.find(p => p._id === selectionData.selectedProvinceId);
    const selectedCity = selectionData.availableCities.find(c => c._id === selectionData.selectedCityId);
    const selectedTownship = selectionData.availableTownships.find(t => t._id === selectionData.selectedTownshipId);

    if (selectedProvince && selectedCity && selectedTownship) {
      // For now, we'll create a location result with the coordinates
      const result: LocationSelectionResult = {
        provinceId: selectedProvince._id,
        provinceName: selectedProvince.name,
        cityId: selectedCity._id,
        cityName: selectedCity.name,
        townshipId: selectedTownship._id,
        townshipName: selectedTownship.name,
        locationId: "", // Will be created later
        locationName: address,
        fullPath: `${address}, ${selectedTownship.name}, ${selectedCity.name}, ${selectedProvince.name}`
      };

      onLocationSelect(result);
      onClose();
    }
  }, [selectionData, onLocationSelect, onClose]);

  // Handle back from map
  const handleMapBack = useCallback(() => {
    setCurrentLevel("township");
  }, []);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'Escape':
          onClose();
          break;
        case 'Backspace':
          if (currentLevel !== "province" && !searchQuery) {
            event.preventDefault();
            handleBack();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, currentLevel, searchQuery, onClose, handleBack]);

  if (!mounted || !isOpen) return null;

  return createPortal(
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-[100000] flex items-center justify-center bg-black/60 backdrop-blur-md"
        onClick={onClose}
        role="dialog"
        aria-modal="true"
        aria-labelledby="location-modal-title"
        aria-describedby="location-modal-description"
      >
        <motion.div
          variants={isMobile ? mobileModalVariants : modalVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className={cn(
            "relative bg-white/95 backdrop-blur-xl shadow-2xl border border-white/20",
            isMobile
              ? "w-full h-full rounded-t-3xl"
              : currentLevel === "map"
                ? "w-full max-w-6xl mx-4 max-h-[90vh] rounded-3xl"
                : "w-full max-w-2xl mx-4 max-h-[85vh] rounded-3xl"
          )}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="relative p-6 border-b border-gray-200/50">
            <div className="flex items-center justify-between">
              {currentLevel !== "province" && (
                <Button
                  onClick={handleBack}
                  variant="ghost"
                  size="icon"
                  className="h-10 w-10 rounded-full bg-gray-100/50 hover:bg-gray-200/50"
                >
                  <ChevronLeft className="h-5 w-5" />
                </Button>
              )}
              
              <div className="flex-1 text-center">
                <h2 id="location-modal-title" className="text-xl font-bold text-gray-900">
                  {title}
                </h2>
                <p id="location-modal-description" className="text-sm text-gray-600 mt-1">
                  {currentLevel === "province" && "Select your province"}
                  {currentLevel === "city" && "Select your city"}
                  {currentLevel === "township" && "Select your township"}
                  {currentLevel === "location" && "Select your location"}
                </p>
              </div>

              <Button
                onClick={onClose}
                variant="ghost"
                size="icon"
                className="h-10 w-10 rounded-full bg-gray-100/50 hover:bg-gray-200/50"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Breadcrumb */}
            {currentLevel !== "province" && (
              <div className="flex items-center gap-2 mt-4 text-sm text-gray-600">
                {selectionData.selectedProvinceId && (
                  <>
                    <span>{selectionData.availableProvinces.find(p => p._id === selectionData.selectedProvinceId)?.name}</span>
                    {selectionData.selectedCityId && (
                      <>
                        <ArrowRight className="h-3 w-3" />
                        <span>{selectionData.availableCities.find(c => c._id === selectionData.selectedCityId)?.name}</span>
                        {selectionData.selectedTownshipId && (
                          <>
                            <ArrowRight className="h-3 w-3" />
                            <span>{selectionData.availableTownships.find(t => t._id === selectionData.selectedTownshipId)?.name}</span>
                          </>
                        )}
                      </>
                    )}
                  </>
                )}
              </div>
            )}
          </div>

          {/* Search */}
          <div className="p-4 border-b border-gray-200/50">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder={`Search ${currentLevel}s...`}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 h-12 bg-gray-50/50 border-gray-200/50 focus:border-blue-500 focus:ring-blue-200"
                aria-label={`Search ${currentLevel}s`}
                autoComplete="off"
                autoFocus={!isMobile} // Auto-focus on desktop only
              />
            </div>
          </div>

          {/* Content */}
          <div className={cn(
            "overflow-y-auto",
            isMobile
              ? "h-[calc(100vh-200px)]"
              : currentLevel === "map"
                ? "max-h-[600px]"
                : "max-h-[400px]"
          )}>
            <AnimatePresence mode="wait">
              <motion.div
                key={currentLevel}
                {...levelTransition}
                className={currentLevel === "map" ? "p-0" : "p-4"}
              >
                {/* Province Level */}
                {currentLevel === "province" && (
                  <ProvinceLevel
                    provinces={selectionData.availableProvinces}
                    searchQuery={searchQuery}
                    onSelect={handleProvinceSelect}
                    isLoading={selectionData.isLoading.provinces}
                  />
                )}

                {/* City Level */}
                {currentLevel === "city" && (
                  <CityLevel
                    cities={selectionData.availableCities}
                    searchQuery={searchQuery}
                    onSelect={handleCitySelect}
                    isLoading={selectionData.isLoading.cities}
                  />
                )}

                {/* Township Level */}
                {currentLevel === "township" && (
                  <TownshipLevel
                    townships={selectionData.availableTownships}
                    searchQuery={searchQuery}
                    onSelect={handleTownshipSelect}
                    isLoading={selectionData.isLoading.townships}
                  />
                )}

                {/* Map Level - NEW: Map-based location selection */}
                {currentLevel === "map" && selectionData.selectedTownship && (
                  <div className="p-0">
                    <MapLocationSelector
                      township={selectionData.selectedTownship}
                      onGroupSelect={handleMapGroupSelect}
                      onNewGroupRequest={handleMapNewGroupRequest}
                      onBack={handleMapBack}
                      userEmail={userEmail}
                      userName={userName}
                      userPhone={userPhone}
                      className="w-full"
                    />
                  </div>
                )}

                {/* Location Level - DEPRECATED: Keep for fallback */}
                {currentLevel === "location" && (
                  <LocationLevel
                    locations={selectionData.availableLocations}
                    searchQuery={searchQuery}
                    onSelect={handleLocationSelect}
                    isLoading={selectionData.isLoading.locations}
                    onGroupRequest={onGroupRequest}
                    onGroupRequestSave={onGroupRequestSave}
                    locationSelection={{
                      provinceId: selectionData.selectedProvince?._id || "",
                      provinceName: selectionData.selectedProvince?.name || "",
                      cityId: selectionData.selectedCity?._id || "",
                      cityName: selectionData.selectedCity?.name || "",
                      townshipId: selectionData.selectedTownship?._id || "",
                      townshipName: selectionData.selectedTownship?.name || "",
                      locationId: "",
                      locationName: "",
                      fullPath: `${selectionData.selectedProvince?.name} > ${selectionData.selectedCity?.name} > ${selectionData.selectedTownship?.name}`
                    }}
                    userEmail={userEmail}
                    userName={userName}
                    userPhone={userPhone}
                  />
                )}
              </motion.div>
            </AnimatePresence>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>,
    document.body
  );
}

// Level Components
interface LevelProps<T> {
  items: T[];
  searchQuery: string;
  onSelect: (item: T) => void;
  isLoading: boolean;
}

function ProvinceLevel({
  provinces,
  searchQuery,
  onSelect,
  isLoading
}: {
  provinces: Province[];
  searchQuery: string;
  onSelect: (province: Province) => void;
  isLoading: boolean;
}) {
  const filteredProvinces = provinces.filter(province =>
    province.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    province.code.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-3 text-gray-600">Loading provinces...</span>
      </div>
    );
  }

  if (filteredProvinces.length === 0) {
    return (
      <div className="text-center py-12">
        <MapPin className="h-12 w-12 text-gray-300 mx-auto mb-4" />
        <p className="text-gray-500">No provinces found</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
      {filteredProvinces.map((province, index) => (
        <motion.button
          key={province._id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.05 }}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => onSelect(province)}
          className="p-4 text-left bg-white/50 hover:bg-blue-50/50 border border-gray-200/50 hover:border-blue-300/50 rounded-xl transition-all duration-200 group focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          aria-label={`Select ${province.name} province`}
        >
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-gray-900 group-hover:text-blue-700">
                {province.name}
              </h3>
              <p className="text-sm text-gray-500 mt-1">
                {province.code}
              </p>
            </div>
            <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-blue-500 transition-colors" />
          </div>
        </motion.button>
      ))}
    </div>
  );
}

function CityLevel({
  cities,
  searchQuery,
  onSelect,
  isLoading
}: {
  cities: City[];
  searchQuery: string;
  onSelect: (city: City) => void;
  isLoading: boolean;
}) {
  const filteredCities = cities.filter(city =>
    city.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-3 text-gray-600">Loading cities...</span>
      </div>
    );
  }

  if (filteredCities.length === 0) {
    return (
      <div className="text-center py-12">
        <Navigation className="h-12 w-12 text-gray-300 mx-auto mb-4" />
        <p className="text-gray-500">No cities found</p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {filteredCities.map((city, index) => (
        <motion.button
          key={city._id}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: index * 0.03 }}
          whileHover={{ scale: 1.01, x: 4 }}
          whileTap={{ scale: 0.99 }}
          onClick={() => onSelect(city)}
          className="w-full p-4 text-left bg-white/50 hover:bg-blue-50/50 border border-gray-200/50 hover:border-blue-300/50 rounded-xl transition-all duration-200 group focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          aria-label={`Select ${city.name} city`}
        >
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-gray-900 group-hover:text-blue-700">
                {city.name}
              </h3>
            </div>
            <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-blue-500 transition-colors" />
          </div>
        </motion.button>
      ))}
    </div>
  );
}

function TownshipLevel({
  townships,
  searchQuery,
  onSelect,
  isLoading
}: {
  townships: Township[];
  searchQuery: string;
  onSelect: (township: Township) => void;
  isLoading: boolean;
}) {
  const filteredTownships = townships.filter(township =>
    township.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-3 text-gray-600">Loading townships...</span>
      </div>
    );
  }

  if (filteredTownships.length === 0) {
    return (
      <div className="text-center py-12">
        <MapPin className="h-12 w-12 text-gray-300 mx-auto mb-4" />
        <p className="text-gray-500">No townships found</p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {filteredTownships.map((township, index) => (
        <motion.button
          key={township._id}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: index * 0.03 }}
          whileHover={{ scale: 1.01, x: 4 }}
          whileTap={{ scale: 0.99 }}
          onClick={() => onSelect(township)}
          className="w-full p-4 text-left bg-white/50 hover:bg-blue-50/50 border border-gray-200/50 hover:border-blue-300/50 rounded-xl transition-all duration-200 group focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          aria-label={`Select ${township.name} township`}
        >
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-gray-900 group-hover:text-blue-700">
                {township.name}
              </h3>
              {township.description && (
                <p className="text-sm text-gray-500 mt-1">
                  {township.description}
                </p>
              )}
            </div>
            <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-blue-500 transition-colors" />
          </div>
        </motion.button>
      ))}
    </div>
  );
}

function LocationLevel({
  locations,
  searchQuery,
  onSelect,
  isLoading,
  onGroupRequest,
  onGroupRequestSave,
  locationSelection,
  userEmail,
  userName,
  userPhone
}: {
  locations: Location[];
  searchQuery: string;
  onSelect: (location: Location) => void;
  isLoading: boolean;
  onGroupRequest?: (data: GroupRequestData & { locationSelection: LocationSelectionResult }) => Promise<void>;
  onGroupRequestSave?: (data: GroupRequestSaveData) => void;
  locationSelection?: LocationSelectionResult;
  userEmail?: string;
  userName?: string;
  userPhone?: string;
}) {
  const [showRequestForm, setShowRequestForm] = useState(false);
  const [isSubmittingRequest, setIsSubmittingRequest] = useState(false);
  const [requestFormData, setRequestFormData] = useState<GroupRequestData>({
    requestedGroupName: "",
    groupDescription: ""
  });
  const [requestErrors, setRequestErrors] = useState<Record<string, string>>({});

  const filteredLocations = locations.filter(location =>
    location.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const validateRequestForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!requestFormData.requestedGroupName.trim()) {
      newErrors.requestedGroupName = "Group name is required";
    } else if (requestFormData.requestedGroupName.trim().length < 3) {
      newErrors.requestedGroupName = "Group name must be at least 3 characters";
    } else if (requestFormData.requestedGroupName.trim().length > 50) {
      newErrors.requestedGroupName = "Group name must be less than 50 characters";
    } else if (!/^[a-zA-Z0-9\s\-_]+$/.test(requestFormData.requestedGroupName.trim())) {
      newErrors.requestedGroupName = "Group name can only contain letters, numbers, spaces, hyphens, and underscores";
    }

    if (requestFormData.groupDescription && requestFormData.groupDescription.trim().length > 500) {
      newErrors.groupDescription = "Description must be less than 500 characters";
    }

    setRequestErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRequestSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    console.log("🔍 Group request form submitted");
    console.log("📊 Available handlers:", {
      onGroupRequestSave: !!onGroupRequestSave,
      onGroupRequest: !!onGroupRequest
    });

    if (!validateRequestForm()) return;

    if (!locationSelection) {
      setRequestErrors({
        submit: "Location information is missing. Please select your location again."
      });
      return;
    }

    const groupRequestData: GroupRequestSaveData = {
      requestedGroupName: requestFormData.requestedGroupName.trim(),
      groupDescription: requestFormData.groupDescription?.trim() || undefined,
      locationSelection
    };

    console.log("📝 Group request data prepared:", groupRequestData);

    // Priority 1: Save to localStorage if onGroupRequestSave is provided (for wizard flow)
    if (onGroupRequestSave) {
      console.log("💾 Using save handler (wizard flow)");
      try {
        onGroupRequestSave(groupRequestData);

        console.log("✅ Group request data saved to wizard");

        // Reset form and hide it
        setRequestFormData({ requestedGroupName: "", groupDescription: "" });
        setShowRequestForm(false);

        // Show success message
        setRequestErrors({});

        return;
      } catch (error) {
        console.error("❌ Error saving group request:", error);
        setRequestErrors({
          submit: "Failed to save request data. Please try again."
        });
        return;
      }
    }

    // Priority 2: Immediate submission if onGroupRequest is provided (for direct submission)
    if (onGroupRequest) {
      console.log("🚀 Using submit handler (direct flow)");
      setIsSubmittingRequest(true);
      setRequestErrors({});

      try {
        await onGroupRequest({
          requestedGroupName: groupRequestData.requestedGroupName,
          groupDescription: groupRequestData.groupDescription,
          locationSelection: groupRequestData.locationSelection
        });

        console.log("✅ Group request submitted successfully");

        // Reset form and hide it
        setRequestFormData({ requestedGroupName: "", groupDescription: "" });
        setShowRequestForm(false);

      } catch (error) {
        console.error("❌ Error submitting group request:", error);
        setRequestErrors({
          submit: error instanceof Error ? error.message : "Failed to submit request. Please try again."
        });
      } finally {
        setIsSubmittingRequest(false);
      }
      return;
    }

    // Fallback: No handlers available
    console.log("❌ No handlers available");
    setRequestErrors({
      submit: "Group request functionality is not available. Please try again later."
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-3 text-gray-600">Loading locations...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Available Locations */}
      {filteredLocations.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Available Groups in This Township</h4>
          {filteredLocations.map((location, index) => (
            <motion.button
              key={location._id}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.03 }}
              whileHover={{ scale: 1.01, x: 4 }}
              whileTap={{ scale: 0.99 }}
              onClick={() => onSelect(location)}
              className="w-full p-4 text-left bg-white/50 hover:bg-green-50/50 border border-gray-200/50 hover:border-green-300/50 rounded-xl transition-all duration-200 group focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
              aria-label={`Select ${location.name} location`}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-gray-900 group-hover:text-green-700">
                    {location.name}
                  </h3>
                  {location.description && (
                    <p className="text-sm text-gray-500 mt-1">
                      {location.description}
                    </p>
                  )}
                </div>
                <Check className="h-5 w-5 text-gray-400 group-hover:text-green-500 transition-colors" />
              </div>
            </motion.button>
          ))}
        </div>
      )}

      {/* Group Request Section - Always Available */}
      <div className="border-t border-gray-200 pt-4">
          {/* Toggle Button */}
          <motion.button
            onClick={() => setShowRequestForm(!showRequestForm)}
            className="w-full p-4 text-left bg-gradient-to-r from-blue-50 to-purple-50 hover:from-blue-100 hover:to-purple-100 border border-blue-200 hover:border-blue-300 rounded-xl transition-all duration-200 group focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            whileHover={{ scale: 1.01 }}
            whileTap={{ scale: 0.99 }}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <Plus className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 group-hover:text-blue-700">
                    Request New Group
                  </h3>
                  <p className="text-sm text-gray-500">
                    {filteredLocations.length === 0
                      ? "No groups available in this township"
                      : "Can't find your exact location?"}
                  </p>
                </div>
              </div>
              {showRequestForm ? (
                <ChevronUp className="h-5 w-5 text-gray-400 group-hover:text-blue-500 transition-colors" />
              ) : (
                <ChevronDown className="h-5 w-5 text-gray-400 group-hover:text-blue-500 transition-colors" />
              )}
            </div>
          </motion.button>

          {/* Request Form */}
          <AnimatePresence>
            {showRequestForm && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                className="overflow-hidden"
              >
                <div className="mt-4 p-4 bg-white border border-gray-200 rounded-xl">
                  {/* Location Display */}
                  <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center gap-2 mb-1">
                      <MapPin className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-800">Request Location</span>
                    </div>
                    <p className="text-sm text-blue-700">
                      {locationSelection?.fullPath || "Location information will be auto-filled"}
                    </p>
                  </div>

                  {/* Form */}
                  <form onSubmit={handleRequestSubmit} className="space-y-4">
                    {/* Group Name */}
                    <div className="space-y-2">
                      <Label htmlFor="groupName" className="text-sm font-medium text-gray-700">
                        Group Name *
                      </Label>
                      <Input
                        id="groupName"
                        type="text"
                        value={requestFormData.requestedGroupName}
                        onChange={(e) => setRequestFormData(prev => ({ ...prev, requestedGroupName: e.target.value }))}
                        placeholder="Enter your group name"
                        className={requestErrors.requestedGroupName ? "border-red-300 focus:border-red-500" : ""}
                        disabled={isSubmittingRequest}
                        maxLength={50}
                      />
                      {requestErrors.requestedGroupName && (
                        <p className="text-sm text-red-600 flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" />
                          {requestErrors.requestedGroupName}
                        </p>
                      )}
                    </div>

                    {/* Group Description */}
                    <div className="space-y-2">
                      <Label htmlFor="groupDescription" className="text-sm font-medium text-gray-700">
                        Description (Optional)
                      </Label>
                      <Textarea
                        id="groupDescription"
                        value={requestFormData.groupDescription}
                        onChange={(e) => setRequestFormData(prev => ({ ...prev, groupDescription: e.target.value }))}
                        placeholder="Briefly describe your group's purpose or goals"
                        className={requestErrors.groupDescription ? "border-red-300 focus:border-red-500" : ""}
                        disabled={isSubmittingRequest}
                        maxLength={500}
                        rows={3}
                      />
                      {requestErrors.groupDescription && (
                        <p className="text-sm text-red-600 flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" />
                          {requestErrors.groupDescription}
                        </p>
                      )}
                      <p className="text-xs text-gray-500">
                        {requestFormData.groupDescription?.length || 0}/500 characters
                      </p>
                    </div>

                    {/* Submit Error */}
                    {requestErrors.submit && (
                      <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>{requestErrors.submit}</AlertDescription>
                      </Alert>
                    )}

                    {/* Info Message */}
                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        Your request will be reviewed by our admin team. Once approved,
                        you'll become the group admin and can start inviting members.
                      </AlertDescription>
                    </Alert>

                    {/* Submit Button */}
                    <div className="flex gap-3">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setShowRequestForm(false)}
                        disabled={isSubmittingRequest}
                        className="flex-1"
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        disabled={isSubmittingRequest}
                        className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium"
                      >
                        {isSubmittingRequest ? (
                          <div className="flex items-center gap-2">
                            <Loader2 className="h-4 w-4 animate-spin" />
                            Submitting...
                          </div>
                        ) : (
                          "Submit Request"
                        )}
                      </Button>
                    </div>
                  </form>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>


    </div>
  );
}
