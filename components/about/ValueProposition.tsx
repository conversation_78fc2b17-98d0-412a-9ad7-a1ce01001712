"use client"

import { useState, useCallback } from "react"
import { Users, MapPin, Truck, PiggyBank, Sparkles, Heart, TrendingUp, Shield } from 'lucide-react'
import { JoinGroupModal } from "@/components/modals/JoinGroupModal"

const features = [
  {
    icon: Users,
    title: "Collective Buying Power",
    description: "Unite with your neighbors to unlock exclusive bulk discounts and premium pricing that individual shoppers can't access.",
    gradient: "from-blue-500 to-purple-600"
  },
  {
    icon: MapPin,
    title: "Hyper-Local Zones",
    description: "Smart zone matching connects you with nearby shoppers for optimized delivery routes and maximum community impact.",
    gradient: "from-green-500 to-teal-600"
  },
  {
    icon: Truck,
    title: "Smart Logistics",
    description: "AI-powered delivery coordination ensures efficient bulk distribution while minimizing environmental impact and costs.",
    gradient: "from-orange-500 to-red-600"
  },
  {
    icon: PiggyBank,
    title: "Guaranteed Value",
    description: "Transparent pricing with guaranteed savings of 20-40% compared to traditional retail through our innovative model.",
    gradient: "from-pink-500 to-rose-600"
  },
  {
    icon: Sparkles,
    title: "Premium Quality",
    description: "Curated selection of high-quality products from trusted suppliers, ensuring freshness and excellence in every order.",
    gradient: "from-yellow-500 to-orange-600"
  },
  {
    icon: Heart,
    title: "Community First",
    description: "Building lasting relationships between neighbors while supporting local economic growth and social cohesion.",
    gradient: "from-red-500 to-pink-600"
  },
  {
    icon: TrendingUp,
    title: "Financial Wellness",
    description: "Empowering families to stretch their budgets further while accessing premium groceries previously out of reach.",
    gradient: "from-indigo-500 to-blue-600"
  },
  {
    icon: Shield,
    title: "Trusted Platform",
    description: "Secure transactions, verified suppliers, and community-driven reviews ensure a safe and reliable shopping experience.",
    gradient: "from-gray-600 to-gray-800"
  }
]

export function ValueProposition() {
  const [isJoinGroupOpen, setIsJoinGroupOpen] = useState(false)

  const openJoinGroupModal = useCallback(() => {
    setIsJoinGroupOpen(true)
  }, [])
  return (
    <section className="relative py-32 px-4 md:px-6 bg-gradient-to-br from-[#2A7C6C] via-[#2A7C6C] to-[#1E5A4F] overflow-hidden">
      {/* Animated Pattern Background */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Cpath d='M0 0h80v80H0V0zm20 20v40h40V20H20zm20 35a15 15 0 1 1 0-30 15 15 0 0 1 0 30z' fill-rule='nonzero'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      {/* Floating Gradient Orbs */}
      <div className="absolute top-10 left-10 w-40 h-40 bg-gradient-to-r from-[#7FDBCA]/30 to-transparent rounded-full blur-3xl" />
      <div className="absolute bottom-10 right-10 w-60 h-60 bg-gradient-to-l from-white/20 to-transparent rounded-full blur-3xl" />
      <div className="absolute top-1/2 left-1/3 w-32 h-32 bg-gradient-to-r from-[#7FDBCA]/20 to-transparent rounded-full blur-2xl" />

      <div className="container mx-auto relative z-10">
        <div className="text-center mb-12 sm:mb-20">
          <div className="inline-block mb-8">
            <h2
              className="text-white text-3xl sm:text-5xl md:text-6xl lg:text-7xl font-bold leading-tight"
              style={{
                fontFamily: 'ClashDisplay-Variable, sans-serif',
                letterSpacing: '-0.03em'
              }}
            >
              Why Choose
              <span className="block text-[#7FDBCA]">Stokvel Grocery Market?</span>
            </h2>
          </div>

          <div className="w-24 sm:w-32 h-1 bg-gradient-to-r from-[#7FDBCA] to-white mx-auto mb-6 sm:mb-8 rounded-full" />

          <p
            className="text-white/90 text-base sm:text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed"
            style={{ fontFamily: 'Avenir, sans-serif' }}
          >
            Discover the revolutionary benefits of community-powered shopping that&apos;s transforming
            how families across South Africa access quality groceries.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="group relative bg-white/10 backdrop-blur-sm p-8 rounded-3xl border border-white/20 hover:bg-white/20 transition-all duration-500 hover:-translate-y-2 hover:shadow-2xl"
            >
              {/* Gradient Background on Hover */}
              <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-10 rounded-3xl transition-opacity duration-500`} />

              <div className="relative z-10">
                <div className={`h-16 w-16 bg-gradient-to-br ${feature.gradient} rounded-2xl flex items-center justify-center mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className="h-8 w-8 text-white" />
                </div>

                <h3
                  className="text-white text-xl md:text-2xl font-bold mb-4 group-hover:text-[#7FDBCA] transition-colors duration-300"
                  style={{
                    fontFamily: 'ClashDisplay-Variable, sans-serif',
                    letterSpacing: '-0.01em'
                  }}
                >
                  {feature.title}
                </h3>

                <p
                  className="text-white/80 leading-relaxed group-hover:text-white transition-colors duration-300"
                  style={{ fontFamily: 'Avenir, sans-serif' }}
                >
                  {feature.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action Section */}
        <div className="mt-20 text-center">
          <div className="bg-white/10 backdrop-blur-sm p-12 rounded-3xl border border-white/20 max-w-4xl mx-auto">
            <h3
              className="text-white text-3xl md:text-4xl font-bold mb-6"
              style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
            >
              Ready to Transform Your Shopping Experience?
            </h3>
            <p
              className="text-white/90 text-lg md:text-xl mb-8 leading-relaxed"
              style={{ fontFamily: 'Avenir, sans-serif' }}
            >
              Join thousands of families who have already discovered the power of community shopping.
              Start saving today while building meaningful connections in your neighborhood.
            </p>
            <div className="flex justify-center">
              <button
                onClick={openJoinGroupModal}
                className="px-8 py-4 bg-white text-[#2A7C6C] rounded-full font-semibold hover:bg-white/95 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1"
                style={{ fontFamily: 'Avenir, sans-serif' }}
              >
                Join Your Community
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Modal */}
      <JoinGroupModal isOpen={isJoinGroupOpen} onClose={() => setIsJoinGroupOpen(false)} />
    </section>
  )
}

