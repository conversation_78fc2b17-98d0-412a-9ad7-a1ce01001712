'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useGetShoppingCartQuery } from '@/lib/redux/features/cart/cartApiSlice';
import { setCart, hideNotification, setError } from '@/lib/redux/features/cart/cartSlice';
import { useAppDispatch } from '@/lib/redux/hooks';

interface CartProviderProps {
  children: React.ReactNode;
  groupId: string | null;
}

/**
 * CartProvider component that fetches and manages cart data using Redux
 *
 * This component should be used at the layout level to provide cart data
 * to all child components. It handles fetching the cart data and updating
 * the Redux store.
 */

export function CartProvider({ children, groupId }: CartProviderProps) {
  const { user, isAuthenticated } = useAuth();
  const userId = user?._id || '';
  const dispatch = useAppDispatch();
  const [retryCount, setRetryCount] = useState(0);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);

  // Clear cart when user logs out
  useEffect(() => {
    if (!isAuthenticated || !user) {
      dispatch(setCart({
        _id: '',
        userId: '',
        groupId: '',
        items: [],
        total: 0,
        isFinalized: false
      }));
      setRetryCount(0);
    }
  }, [isAuthenticated, user, dispatch]);

  // Store the current group ID in localStorage for use in other components
  useEffect(() => {
    if (groupId) {
      localStorage.setItem('currentGroupId', groupId);
      console.log('CartProvider - Set currentGroupId in localStorage:', groupId);
    } else {
      localStorage.removeItem('currentGroupId');
      console.log('CartProvider - Removed currentGroupId from localStorage');
    }
  }, [groupId]);

  // Fetch cart data using RTK Query with reduced polling for stability
  const { data: cart, isLoading, error, refetch } = useGetShoppingCartQuery({
    userId,
    groupId: groupId || undefined // Convert null to undefined for API
  }, {
    skip: !userId || !user || !isAuthenticated || userId.trim() === '', // Skip if no valid user
    pollingInterval: 60000, // Reduced to 60 seconds to prevent excessive re-renders
    refetchOnMountOrArgChange: true,
    refetchOnFocus: false, // Disable automatic refetch on focus to prevent conflicts
    refetchOnReconnect: true,
  });

  // Reduced debug logging for stability
  // console.log('CartProvider - groupId:', groupId);
  // console.log('CartProvider - userId:', userId);
  // console.log('CartProvider - cart data:', cart);

  // Log any errors
  if (error) {
    console.error('CartProvider - Error fetching cart:', error);
  }

  // Update Redux store when cart data changes
  useEffect(() => {
    if (cart) {
      // Only log in development
      if (process.env.NODE_ENV === 'development') {
        console.log('CartProvider - Cart data received:', cart);
      }
      dispatch(setCart(cart));
      setLastSyncTime(new Date());
      setRetryCount(0); // Reset retry count on successful fetch
    } else if (!isLoading && userId && groupId) {
      // If cart is null but we're not loading and have a userId, set empty cart
      if (process.env.NODE_ENV === 'development') {
        console.log('CartProvider - Setting empty cart for user:', userId, 'in group:', groupId);
      }
      dispatch(setCart({
        _id: '',
        userId,
        groupId,
        items: [],
        total: 0,
        isFinalized: false
      }));
      setLastSyncTime(new Date());
    }
  }, [cart, isLoading, userId, groupId, dispatch]);

  // Handle errors with retry logic
  useEffect(() => {
    if (error) {
      // Improved error logging and handling
      const errorMessage = error && typeof error === 'object'
        ? JSON.stringify(error, null, 2)
        : String(error);

      console.error('Error fetching cart:', errorMessage);

      // Only dispatch error and retry if we have a valid userId
      if (userId) {
        dispatch(setError('Failed to load cart. Retrying...'));

        // Implement exponential backoff for retries
        if (retryCount < 3) { // Reduced retry count to prevent excessive requests
          const backoffTime = Math.min(1000 * Math.pow(2, retryCount), 10000);
          console.log(`Retrying cart fetch in ${backoffTime}ms (attempt ${retryCount + 1})`);

          const retryTimeout = setTimeout(() => {
            refetch();
            setRetryCount(prev => prev + 1);
          }, backoffTime);

          return () => clearTimeout(retryTimeout);
        } else {
          // After max retries, set a fallback empty cart
          console.log('Max retries reached, setting empty cart');
          dispatch(setCart({
            _id: '',
            userId,
            groupId: groupId || '',
            items: [],
            total: 0,
            isFinalized: false
          }));
          dispatch(setError(''));
        }
      }
    } else {
      // Clear retry count when there's no error
      setRetryCount(0);
    }
  }, [error, retryCount, dispatch, refetch, userId, groupId]);

  // Sync cart on visibility change (tab focus)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && userId) {
        // Only refetch if it's been more than 5 seconds since last sync
        if (!lastSyncTime || new Date().getTime() - lastSyncTime.getTime() > 5000) {
          console.log('Tab focused, refreshing cart data');
          refetch();
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [userId, refetch, lastSyncTime]);

  // Notification timeout effect
  useEffect(() => {
    const notificationTimeout = setTimeout(() => {
      dispatch(hideNotification());
    }, 3000);

    // Clean up timeout on unmount
    return () => {
      clearTimeout(notificationTimeout);
    };
  }, [dispatch]);

  return <>{children}</>;
}
