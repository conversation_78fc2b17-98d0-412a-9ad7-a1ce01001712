// components/admin/AdminLocationMap.tsx

"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import Map, { Marker, Popup, NavigationControl, GeolocateControl } from 'react-map-gl';
import { MapPin, Users, Edit, Trash2, Eye, Navigation, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { motion, AnimatePresence } from 'framer-motion';
import { GroupMarker } from '@/components/maps/GroupMarker';
import { ClusteredGroupMarkers, useClusterStats } from '@/components/maps/ClusteredGroupMarkers';
import type { StokvelGroup } from '@/types/stokvelgroup';
import type { Province, City, Township } from '@/types/locations';

// Mapbox access token
const MAPBOX_TOKEN = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN || '';

export interface AdminLocationMapProps {
  onGroupEdit?: (group: StokvelGroup) => void;
  onGroupDelete?: (group: StokvelGroup) => void;
  onGroupView?: (group: StokvelGroup) => void;
  className?: string;
}

interface ViewState {
  longitude: number;
  latitude: number;
  zoom: number;
}

interface FilterState {
  province?: string;
  city?: string;
  township?: string;
  status?: 'all' | 'active' | 'inactive';
}

export function AdminLocationMap({
  onGroupEdit,
  onGroupDelete,
  onGroupView,
  className = ""
}: AdminLocationMapProps) {
  const mapRef = useRef<any>(null);
  const [viewState, setViewState] = useState<ViewState>({
    longitude: 28.0473, // Default to Johannesburg
    latitude: -26.2041,
    zoom: 6
  });
  
  const [groups, setGroups] = useState<StokvelGroup[]>([]);
  const [filteredGroups, setFilteredGroups] = useState<StokvelGroup[]>([]);
  const [selectedGroup, setSelectedGroup] = useState<StokvelGroup | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [mapStyle, setMapStyle] = useState('mapbox://styles/mapbox/streets-v12');
  const [enableClustering, setEnableClustering] = useState(true);

  // Cluster statistics
  const clusterStats = useClusterStats(filteredGroups, viewState.zoom);
  
  // Filter state
  const [filters, setFilters] = useState<FilterState>({
    status: 'all'
  });
  const [provinces, setProvinces] = useState<Province[]>([]);
  const [cities, setCities] = useState<City[]>([]);
  const [townships, setTownships] = useState<Township[]>([]);

  // Fetch all groups with coordinates
  useEffect(() => {
    const fetchGroups = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/stokvel-groups/with-coordinates');
        if (!response.ok) throw new Error('Failed to fetch groups');
        
        const data = await response.json();
        setGroups(data.groups || []);
      } catch (error) {
        console.error('Error fetching groups:', error);
        setGroups([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchGroups();
  }, []);

  // Fetch location hierarchy for filters
  useEffect(() => {
    const fetchLocations = async () => {
      try {
        const [provincesRes, citiesRes, townshipsRes] = await Promise.all([
          fetch('/api/locations/provinces'),
          fetch('/api/locations/cities'),
          fetch('/api/locations/townships')
        ]);

        if (provincesRes.ok) {
          const provincesData = await provincesRes.json();
          setProvinces(provincesData.provinces || []);
        }

        if (citiesRes.ok) {
          const citiesData = await citiesRes.json();
          setCities(citiesData.cities || []);
        }

        if (townshipsRes.ok) {
          const townshipsData = await townshipsRes.json();
          setTownships(townshipsData.townships || []);
        }
      } catch (error) {
        console.error('Error fetching locations:', error);
      }
    };

    fetchLocations();
  }, []);

  // Apply filters
  useEffect(() => {
    let filtered = groups;

    if (filters.province) {
      // Filter by province (would need location hierarchy data)
      // For now, we'll implement basic filtering
    }

    if (filters.status && filters.status !== 'all') {
      // Filter by status (assuming active groups have members)
      if (filters.status === 'active') {
        filtered = filtered.filter(group => (group.members?.length || 0) > 0);
      } else {
        filtered = filtered.filter(group => (group.members?.length || 0) === 0);
      }
    }

    setFilteredGroups(filtered);
  }, [groups, filters]);

  // Center map on filtered groups
  useEffect(() => {
    if (filteredGroups.length > 0) {
      const validGroups = filteredGroups.filter(group => 
        group.coordinates?.latitude && group.coordinates?.longitude
      );
      
      if (validGroups.length > 0) {
        const avgLat = validGroups.reduce((sum, group) => 
          sum + (group.coordinates?.latitude || 0), 0) / validGroups.length;
        const avgLng = validGroups.reduce((sum, group) => 
          sum + (group.coordinates?.longitude || 0), 0) / validGroups.length;
        
        setViewState(prev => ({
          ...prev,
          longitude: avgLng,
          latitude: avgLat,
          zoom: validGroups.length === 1 ? 14 : 8
        }));
      }
    }
  }, [filteredGroups]);

  // Handle group marker click
  const handleGroupClick = useCallback((group: StokvelGroup) => {
    setSelectedGroup(group);
  }, []);

  // Toggle map style
  const toggleMapStyle = useCallback(() => {
    setMapStyle(prev => 
      prev === 'mapbox://styles/mapbox/streets-v12' 
        ? 'mapbox://styles/mapbox/satellite-streets-v12'
        : 'mapbox://styles/mapbox/streets-v12'
    );
  }, []);

  if (!MAPBOX_TOKEN) {
    return (
      <Card className="w-full h-96 flex items-center justify-center">
        <CardContent>
          <p className="text-center text-gray-500">
            Map functionality requires Mapbox configuration
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Filters */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="w-5 h-5 text-blue-600" />
            Map Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">Province</label>
              <Select
                value={filters.province || ""}
                onValueChange={(value) => setFilters(prev => ({ ...prev, province: value || undefined }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Provinces" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Provinces</SelectItem>
                  {provinces.map((province) => (
                    <SelectItem key={province._id} value={province._id}>
                      {province.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">Status</label>
              <Select
                value={filters.status || "all"}
                onValueChange={(value) => setFilters(prev => ({ ...prev, status: value as any }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Groups</SelectItem>
                  <SelectItem value="active">Active Groups</SelectItem>
                  <SelectItem value="inactive">Inactive Groups</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button
                onClick={() => setFilters({ status: 'all' })}
                variant="outline"
                className="w-full"
              >
                Clear Filters
              </Button>
            </div>

            <div className="flex items-end">
              <div className="text-sm text-gray-600">
                Showing {filteredGroups.length} of {groups.length} groups
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Map */}
      <div className="relative w-full h-[600px] rounded-xl overflow-hidden border border-gray-200">
        {isLoading ? (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent mx-auto mb-2" />
              <p className="text-gray-600">Loading groups...</p>
            </div>
          </div>
        ) : (
          <Map
            ref={mapRef}
            {...viewState}
            onMove={evt => setViewState(evt.viewState)}
            mapStyle={mapStyle}
            mapboxAccessToken={MAPBOX_TOKEN}
            style={{ width: '100%', height: '100%' }}
          >
            {/* Navigation Controls */}
            <NavigationControl position="top-right" />
            <GeolocateControl position="top-right" />

            {/* Group Markers (Clustered or Individual) */}
            {enableClustering ? (
              <ClusteredGroupMarkers
                groups={filteredGroups}
                onGroupClick={handleGroupClick}
                selectedGroupId={selectedGroup?._id}
                zoom={viewState.zoom}
              />
            ) : (
              filteredGroups.map((group) => (
                group.coordinates?.latitude && group.coordinates?.longitude && (
                  <GroupMarker
                    key={group._id}
                    group={group}
                    onClick={() => handleGroupClick(group)}
                    isSelected={selectedGroup?._id === group._id}
                  />
                )
              ))
            )}

            {/* Group Info Popup */}
            {selectedGroup && selectedGroup.coordinates && (
              <Popup
                longitude={selectedGroup.coordinates.longitude}
                latitude={selectedGroup.coordinates.latitude}
                anchor="top"
                onClose={() => setSelectedGroup(null)}
                closeButton={true}
                closeOnClick={false}
              >
                <div className="p-3 min-w-[250px]">
                  <h3 className="font-semibold text-gray-900 mb-2">{selectedGroup.name}</h3>
                  <p className="text-sm text-gray-600 mb-3">{selectedGroup.description}</p>
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4 text-gray-500" />
                      <span className="text-sm text-gray-600">
                        {selectedGroup.members?.length || 0} members
                      </span>
                    </div>
                    {selectedGroup.address && (
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-gray-500" />
                        <span className="text-sm text-gray-600">{selectedGroup.address}</span>
                      </div>
                    )}
                  </div>

                  <div className="flex gap-2">
                    {onGroupView && (
                      <Button
                        onClick={() => onGroupView(selectedGroup)}
                        size="sm"
                        variant="outline"
                        className="flex-1"
                      >
                        <Eye className="w-3 h-3 mr-1" />
                        View
                      </Button>
                    )}
                    {onGroupEdit && (
                      <Button
                        onClick={() => onGroupEdit(selectedGroup)}
                        size="sm"
                        className="flex-1"
                      >
                        <Edit className="w-3 h-3 mr-1" />
                        Edit
                      </Button>
                    )}
                    {onGroupDelete && (
                      <Button
                        onClick={() => onGroupDelete(selectedGroup)}
                        size="sm"
                        variant="destructive"
                        className="flex-1"
                      >
                        <Trash2 className="w-3 h-3 mr-1" />
                        Delete
                      </Button>
                    )}
                  </div>
                </div>
              </Popup>
            )}
          </Map>
        )}

        {/* Map Controls Overlay */}
        <div className="absolute top-4 left-4 flex flex-col gap-2">
          <Button
            onClick={toggleMapStyle}
            variant="outline"
            size="sm"
            className="bg-white/90 backdrop-blur-sm"
          >
            <Navigation className="w-4 h-4 mr-2" />
            {mapStyle.includes('satellite') ? 'Street' : 'Satellite'}
          </Button>

          {filteredGroups.length > 3 && (
            <Button
              onClick={() => setEnableClustering(!enableClustering)}
              variant="outline"
              size="sm"
              className="bg-white/90 backdrop-blur-sm"
            >
              <Users className="w-4 h-4 mr-2" />
              {enableClustering ? 'Individual' : 'Cluster'}
            </Button>
          )}
        </div>

        {/* Stats Overlay */}
        <div className="absolute bottom-4 left-4 right-4">
          <Card className="bg-white/95 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="grid grid-cols-3 md:grid-cols-5 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">{filteredGroups.length}</div>
                  <div className="text-sm text-gray-600">Total Groups</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {filteredGroups.reduce((sum, group) => sum + (group.members?.length || 0), 0)}
                  </div>
                  <div className="text-sm text-gray-600">Total Members</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600">
                    {filteredGroups.filter(group => group.coordinates?.latitude && group.coordinates?.longitude).length}
                  </div>
                  <div className="text-sm text-gray-600">With Coordinates</div>
                </div>
                {enableClustering && clusterStats.clusteringActive && (
                  <>
                    <div>
                      <div className="text-2xl font-bold text-orange-600">{clusterStats.totalClusters}</div>
                      <div className="text-sm text-gray-600">Clusters</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-red-600">{clusterStats.clusteredGroups}</div>
                      <div className="text-sm text-gray-600">Clustered</div>
                    </div>
                  </>
                )}
              </div>

              {enableClustering && clusterStats.clusteringActive && (
                <div className="mt-2 text-xs text-gray-500 text-center">
                  Clustering active • Zoom: {viewState.zoom.toFixed(1)} • {clusterStats.individualMarkers} individual markers
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
