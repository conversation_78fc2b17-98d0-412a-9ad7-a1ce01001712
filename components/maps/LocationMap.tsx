// components/maps/LocationMap.tsx

"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import Map, { Marker, Popup, NavigationControl, GeolocateControl } from 'react-map-gl';
import { MapPin, Plus, Users, Navigation } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { motion, AnimatePresence } from 'framer-motion';
import { GroupMarker } from './GroupMarker';
import { ClusteredGroupMarkers, useClusterStats } from './ClusteredGroupMarkers';
import { LocationSelector } from './LocationSelector';
import { NewGroupModal } from './NewGroupModal';
import type { StokvelGroup } from '@/types/stokvelgroup';
import type { Township } from '@/types/locations';

// Mapbox access token - should be in environment variables
const MAPBOX_TOKEN = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN || '';

export interface LocationMapProps {
  township: Township;
  existingGroups: StokvelGroup[];
  onGroupSelect: (group: StokvelGroup) => void;
  onNewGroupRequest: (coordinates: { latitude: number; longitude: number }, address: string) => void;
  userEmail?: string;
  userName?: string;
  userPhone?: string;
  className?: string;
}

interface ViewState {
  longitude: number;
  latitude: number;
  zoom: number;
}

interface SelectedLocation {
  latitude: number;
  longitude: number;
  address?: string;
}

export function LocationMap({
  township,
  existingGroups,
  onGroupSelect,
  onNewGroupRequest,
  userEmail,
  userName,
  userPhone,
  className = ""
}: LocationMapProps) {
  const mapRef = useRef<any>(null);
  const [viewState, setViewState] = useState<ViewState>({
    longitude: 28.0473, // Default to Johannesburg
    latitude: -26.2041,
    zoom: 12
  });
  
  const [selectedGroup, setSelectedGroup] = useState<StokvelGroup | null>(null);
  const [selectedLocation, setSelectedLocation] = useState<SelectedLocation | null>(null);
  const [isNewGroupModalOpen, setIsNewGroupModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [mapStyle, setMapStyle] = useState('mapbox://styles/mapbox/streets-v12');
  const [enableClustering, setEnableClustering] = useState(true);

  // Cluster statistics
  const clusterStats = useClusterStats(existingGroups, viewState.zoom);

  // Initialize map view based on township or existing groups
  useEffect(() => {
    if (existingGroups.length > 0) {
      // Center map on existing groups
      const validGroups = existingGroups.filter(group => 
        group.coordinates?.latitude && group.coordinates?.longitude
      );
      
      if (validGroups.length > 0) {
        const avgLat = validGroups.reduce((sum, group) => 
          sum + (group.coordinates?.latitude || 0), 0) / validGroups.length;
        const avgLng = validGroups.reduce((sum, group) => 
          sum + (group.coordinates?.longitude || 0), 0) / validGroups.length;
        
        setViewState({
          longitude: avgLng,
          latitude: avgLat,
          zoom: 13
        });
      }
    }
    setIsLoading(false);
  }, [existingGroups]);

  // Handle map click for new location selection
  const handleMapClick = useCallback(async (event: any) => {
    const { lng, lat } = event.lngLat;
    
    try {
      // Reverse geocoding to get address
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${lng},${lat}.json?access_token=${MAPBOX_TOKEN}&types=address`
      );
      const data = await response.json();
      const address = data.features[0]?.place_name || `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
      
      setSelectedLocation({
        latitude: lat,
        longitude: lng,
        address
      });
    } catch (error) {
      console.error('Error getting address:', error);
      setSelectedLocation({
        latitude: lat,
        longitude: lng,
        address: `${lat.toFixed(6)}, ${lng.toFixed(6)}`
      });
    }
  }, []);

  // Handle group marker click
  const handleGroupClick = useCallback((group: StokvelGroup) => {
    setSelectedGroup(group);
    setSelectedLocation(null);
  }, []);

  // Handle new group creation
  const handleCreateNewGroup = useCallback(() => {
    if (selectedLocation) {
      setIsNewGroupModalOpen(true);
    }
  }, [selectedLocation]);

  // Handle new group request submission
  const handleNewGroupSubmit = useCallback((groupData: any) => {
    if (selectedLocation) {
      onNewGroupRequest(
        { latitude: selectedLocation.latitude, longitude: selectedLocation.longitude },
        selectedLocation.address || ''
      );
      setSelectedLocation(null);
      setIsNewGroupModalOpen(false);
    }
  }, [selectedLocation, onNewGroupRequest]);

  // Toggle map style
  const toggleMapStyle = useCallback(() => {
    setMapStyle(prev => 
      prev === 'mapbox://styles/mapbox/streets-v12' 
        ? 'mapbox://styles/mapbox/satellite-streets-v12'
        : 'mapbox://styles/mapbox/streets-v12'
    );
  }, []);

  if (!MAPBOX_TOKEN) {
    return (
      <Card className="w-full h-96 flex items-center justify-center">
        <CardContent>
          <p className="text-center text-gray-500">
            Map functionality requires Mapbox configuration
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`relative w-full h-96 md:h-[500px] rounded-xl overflow-hidden ${className}`}>
      <Map
        ref={mapRef}
        {...viewState}
        onMove={evt => setViewState(evt.viewState)}
        onClick={handleMapClick}
        mapStyle={mapStyle}
        mapboxAccessToken={MAPBOX_TOKEN}
        style={{ width: '100%', height: '100%' }}
        cursor="crosshair"
      >
        {/* Navigation Controls */}
        <NavigationControl position="top-right" />
        <GeolocateControl position="top-right" />

        {/* Group Markers (Clustered or Individual) */}
        {enableClustering ? (
          <ClusteredGroupMarkers
            groups={existingGroups}
            onGroupClick={handleGroupClick}
            selectedGroupId={selectedGroup?._id}
            zoom={viewState.zoom}
          />
        ) : (
          existingGroups.map((group) => (
            group.coordinates?.latitude && group.coordinates?.longitude && (
              <GroupMarker
                key={group._id}
                group={group}
                onClick={() => handleGroupClick(group)}
                isSelected={selectedGroup?._id === group._id}
              />
            )
          ))
        )}

        {/* Selected Location Marker */}
        {selectedLocation && (
          <Marker
            longitude={selectedLocation.longitude}
            latitude={selectedLocation.latitude}
            anchor="bottom"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="relative"
            >
              <div className="w-8 h-8 bg-blue-500 rounded-full border-4 border-white shadow-lg flex items-center justify-center">
                <Plus className="w-4 h-4 text-white" />
              </div>
              <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-blue-500"></div>
            </motion.div>
          </Marker>
        )}

        {/* Group Info Popup */}
        {selectedGroup && selectedGroup.coordinates && (
          <Popup
            longitude={selectedGroup.coordinates.longitude}
            latitude={selectedGroup.coordinates.latitude}
            anchor="top"
            onClose={() => setSelectedGroup(null)}
            closeButton={true}
            closeOnClick={false}
          >
            <div className="p-3 min-w-[200px]">
              <h3 className="font-semibold text-gray-900 mb-2">{selectedGroup.name}</h3>
              <p className="text-sm text-gray-600 mb-3">{selectedGroup.description}</p>
              <div className="flex items-center gap-2 mb-3">
                <Users className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-600">
                  {selectedGroup.members?.length || 0} members
                </span>
              </div>
              <Button
                onClick={() => onGroupSelect(selectedGroup)}
                className="w-full bg-green-600 hover:bg-green-700"
                size="sm"
              >
                Join Group
              </Button>
            </div>
          </Popup>
        )}
      </Map>

      {/* Map Controls Overlay */}
      <div className="absolute top-4 left-4 flex flex-col gap-2">
        <Button
          onClick={toggleMapStyle}
          variant="outline"
          size="sm"
          className="bg-white/90 backdrop-blur-sm"
        >
          <Navigation className="w-4 h-4 mr-2" />
          {mapStyle.includes('satellite') ? 'Street' : 'Satellite'}
        </Button>

        {existingGroups.length > 3 && (
          <Button
            onClick={() => setEnableClustering(!enableClustering)}
            variant="outline"
            size="sm"
            className="bg-white/90 backdrop-blur-sm"
          >
            <Users className="w-4 h-4 mr-2" />
            {enableClustering ? 'Individual' : 'Cluster'}
          </Button>
        )}
      </div>

      {/* Selected Location Info */}
      <AnimatePresence>
        {selectedLocation && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="absolute bottom-4 left-4 right-4"
          >
            <Card className="bg-white/95 backdrop-blur-sm">
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 mb-1">Selected Location</h4>
                    <p className="text-sm text-gray-600 mb-3">{selectedLocation.address}</p>
                    <div className="flex gap-2">
                      <Button
                        onClick={handleCreateNewGroup}
                        className="bg-blue-600 hover:bg-blue-700"
                        size="sm"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Request New Group
                      </Button>
                      <Button
                        onClick={() => setSelectedLocation(null)}
                        variant="outline"
                        size="sm"
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Info Overlay - Only show when no location is selected */}
      {!selectedLocation && (
        <div className="absolute bottom-4 left-4 right-4">
          <Card className="bg-white/95 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">{existingGroups.length}</div>
                  <div className="text-sm text-gray-600">Groups Available</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {existingGroups.reduce((sum, group) => sum + (group.members?.length || 0), 0)}
                  </div>
                  <div className="text-sm text-gray-600">Total Members</div>
                </div>
                {enableClustering && clusterStats.clusteringActive && (
                  <>
                    <div>
                      <div className="text-2xl font-bold text-purple-600">{clusterStats.totalClusters}</div>
                      <div className="text-sm text-gray-600">Clusters</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-orange-600">{clusterStats.clusteredGroups}</div>
                      <div className="text-sm text-gray-600">Clustered</div>
                    </div>
                  </>
                )}
              </div>

              {enableClustering && clusterStats.clusteringActive && (
                <div className="mt-2 text-xs text-gray-500 text-center">
                  Zoom in to see individual groups • {clusterStats.individualMarkers} individual markers
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* New Group Modal */}
      <NewGroupModal
        isOpen={isNewGroupModalOpen}
        onClose={() => setIsNewGroupModalOpen(false)}
        onSubmit={handleNewGroupSubmit}
        selectedLocation={selectedLocation}
        township={township}
        userEmail={userEmail}
        userName={userName}
        userPhone={userPhone}
      />
    </div>
  );
}
