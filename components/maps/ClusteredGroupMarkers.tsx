// components/maps/ClusteredGroupMarkers.tsx

"use client";

import { useMemo } from 'react';
import { Marker } from 'react-map-gl';
import { MapPin, Users } from 'lucide-react';
import { motion } from 'framer-motion';
import { GroupMarker } from './GroupMarker';
import type { StokvelGroup } from '@/types/stokvelgroup';

export interface ClusteredGroupMarkersProps {
  groups: StokvelGroup[];
  onGroupClick: (group: StokvelGroup) => void;
  selectedGroupId?: string;
  zoom: number;
  className?: string;
}

interface ClusterPoint {
  id: string;
  latitude: number;
  longitude: number;
  groups: StokvelGroup[];
  isCluster: boolean;
}

// Clustering configuration
const CLUSTER_RADIUS = 50; // pixels
const MIN_ZOOM_FOR_CLUSTERING = 10;

// Calculate distance between two points in pixels (approximate)
function getPixelDistance(
  lat1: number, 
  lng1: number, 
  lat2: number, 
  lng2: number, 
  zoom: number
): number {
  const earthRadius = 6371000; // meters
  const pixelsPerMeter = (256 * Math.pow(2, zoom)) / (earthRadius * 2 * Math.PI);
  
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng/2) * Math.sin(dLng/2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = earthRadius * c;
  
  return distance * pixelsPerMeter;
}

// Cluster groups based on proximity
function clusterGroups(groups: StokvelGroup[], zoom: number): ClusterPoint[] {
  // Don't cluster at high zoom levels or with few groups
  if (zoom >= MIN_ZOOM_FOR_CLUSTERING || groups.length <= 3) {
    return groups
      .filter(group => group.coordinates?.latitude && group.coordinates?.longitude)
      .map(group => ({
        id: group._id,
        latitude: group.coordinates!.latitude,
        longitude: group.coordinates!.longitude,
        groups: [group],
        isCluster: false
      }));
  }

  const validGroups = groups.filter(group => 
    group.coordinates?.latitude && group.coordinates?.longitude
  );

  const clusters: ClusterPoint[] = [];
  const processed = new Set<string>();

  for (const group of validGroups) {
    if (processed.has(group._id)) continue;

    const cluster: ClusterPoint = {
      id: `cluster-${group._id}`,
      latitude: group.coordinates!.latitude,
      longitude: group.coordinates!.longitude,
      groups: [group],
      isCluster: false
    };

    processed.add(group._id);

    // Find nearby groups to cluster
    for (const otherGroup of validGroups) {
      if (processed.has(otherGroup._id)) continue;

      const distance = getPixelDistance(
        group.coordinates!.latitude,
        group.coordinates!.longitude,
        otherGroup.coordinates!.latitude,
        otherGroup.coordinates!.longitude,
        zoom
      );

      if (distance <= CLUSTER_RADIUS) {
        cluster.groups.push(otherGroup);
        processed.add(otherGroup._id);
        
        // Update cluster center (weighted average)
        const totalGroups = cluster.groups.length;
        cluster.latitude = cluster.groups.reduce((sum, g) => sum + g.coordinates!.latitude, 0) / totalGroups;
        cluster.longitude = cluster.groups.reduce((sum, g) => sum + g.coordinates!.longitude, 0) / totalGroups;
      }
    }

    // Mark as cluster if it contains multiple groups
    if (cluster.groups.length > 1) {
      cluster.isCluster = true;
      cluster.id = `cluster-${cluster.groups.map(g => g._id).join('-')}`;
    }

    clusters.push(cluster);
  }

  return clusters;
}

export function ClusteredGroupMarkers({
  groups,
  onGroupClick,
  selectedGroupId,
  zoom,
  className = ""
}: ClusteredGroupMarkersProps) {
  
  const clusters = useMemo(() => {
    return clusterGroups(groups, zoom);
  }, [groups, zoom]);

  const handleClusterClick = (cluster: ClusterPoint) => {
    if (cluster.isCluster) {
      // For clusters, we could implement zoom-to-bounds or show a popup with group list
      // For now, just click the first group
      onGroupClick(cluster.groups[0]);
    } else {
      onGroupClick(cluster.groups[0]);
    }
  };

  return (
    <div className={className}>
      {clusters.map((cluster) => (
        cluster.isCluster ? (
          // Cluster Marker
          <Marker
            key={cluster.id}
            longitude={cluster.longitude}
            latitude={cluster.latitude}
            anchor="bottom"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              className="relative cursor-pointer"
              onClick={() => handleClusterClick(cluster)}
            >
              {/* Cluster Background */}
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full border-4 border-white shadow-lg flex items-center justify-center">
                <Users className="w-6 h-6 text-white" />
              </div>

              {/* Cluster Count */}
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full min-w-[24px] h-6 flex items-center justify-center px-1 border-2 border-white shadow-sm font-bold"
              >
                {cluster.groups.length}
              </motion.div>

              {/* Pointer */}
              <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-blue-500" />

              {/* Pulse Animation */}
              <motion.div
                className="absolute inset-0 rounded-full border-2 border-blue-400"
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.7, 0, 0.7]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />

              {/* Hover Tooltip */}
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                <div className="bg-black text-white text-xs rounded px-2 py-1 whitespace-nowrap">
                  {cluster.groups.length} groups in this area
                </div>
              </div>
            </motion.div>
          </Marker>
        ) : (
          // Individual Group Marker
          <GroupMarker
            key={cluster.groups[0]._id}
            group={cluster.groups[0]}
            onClick={() => onGroupClick(cluster.groups[0])}
            isSelected={selectedGroupId === cluster.groups[0]._id}
          />
        )
      ))}
    </div>
  );
}

// Hook for cluster statistics
export function useClusterStats(groups: StokvelGroup[], zoom: number) {
  return useMemo(() => {
    const clusters = clusterGroups(groups, zoom);
    const clusterCount = clusters.filter(c => c.isCluster).length;
    const individualCount = clusters.filter(c => !c.isCluster).length;
    const totalGroups = groups.filter(g => g.coordinates?.latitude && g.coordinates?.longitude).length;
    
    return {
      totalClusters: clusterCount,
      individualMarkers: individualCount,
      totalGroups,
      clusteredGroups: totalGroups - individualCount,
      clusteringActive: zoom < MIN_ZOOM_FOR_CLUSTERING && totalGroups > 3
    };
  }, [groups, zoom]);
}
