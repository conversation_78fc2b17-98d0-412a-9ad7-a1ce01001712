// components/maps/NewGroupModal.tsx

"use client";

import { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, MapPin, Users, FileText, Mail, Phone, User } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import type { Township } from '@/types/locations';

export interface NewGroupModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (groupData: GroupRequestData) => void;
  selectedLocation: {
    latitude: number;
    longitude: number;
    address?: string;
  } | null;
  township: Township;
  userEmail?: string;
  userName?: string;
  userPhone?: string;
}

export interface GroupRequestData {
  groupName: string;
  description: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  address: string;
  userEmail: string;
  userName: string;
  userPhone?: string;
}

export function NewGroupModal({
  isOpen,
  onClose,
  onSubmit,
  selectedLocation,
  township,
  userEmail = '',
  userName = '',
  userPhone = ''
}: NewGroupModalProps) {
  const [formData, setFormData] = useState({
    groupName: '',
    description: '',
    userEmail: userEmail,
    userName: userName,
    userPhone: userPhone
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Handle form field changes
  const handleFieldChange = useCallback((field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  }, [errors]);

  // Validate form
  const validateForm = useCallback(() => {
    const newErrors: Record<string, string> = {};

    if (!formData.groupName.trim()) {
      newErrors.groupName = 'Group name is required';
    } else if (formData.groupName.length < 3) {
      newErrors.groupName = 'Group name must be at least 3 characters';
    } else if (formData.groupName.length > 50) {
      newErrors.groupName = 'Group name must be less than 50 characters';
    }

    if (!formData.userEmail.trim()) {
      newErrors.userEmail = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.userEmail)) {
      newErrors.userEmail = 'Please enter a valid email address';
    }

    if (!formData.userName.trim()) {
      newErrors.userName = 'Name is required';
    } else if (formData.userName.length < 2) {
      newErrors.userName = 'Name must be at least 2 characters';
    }

    if (formData.userPhone && !/^[\+]?[0-9\s\-\(\)]{10,}$/.test(formData.userPhone)) {
      newErrors.userPhone = 'Please enter a valid phone number';
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Handle form submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedLocation) return;
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      const groupData: GroupRequestData = {
        groupName: formData.groupName.trim(),
        description: formData.description.trim(),
        coordinates: {
          latitude: selectedLocation.latitude,
          longitude: selectedLocation.longitude
        },
        address: selectedLocation.address || `${selectedLocation.latitude.toFixed(6)}, ${selectedLocation.longitude.toFixed(6)}`,
        userEmail: formData.userEmail.trim(),
        userName: formData.userName.trim(),
        userPhone: formData.userPhone.trim() || undefined
      };

      await onSubmit(groupData);
      
      // Reset form
      setFormData({
        groupName: '',
        description: '',
        userEmail: userEmail,
        userName: userName,
        userPhone: userPhone
      });
      setErrors({});
    } catch (error) {
      console.error('Error submitting group request:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, selectedLocation, validateForm, onSubmit, userEmail, userName, userPhone]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          onClick={(e) => e.stopPropagation()}
          className="w-full max-w-2xl max-h-[90vh] overflow-y-auto"
        >
          <Card className="bg-white shadow-2xl">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-xl flex items-center gap-2">
                  <Users className="w-6 h-6 text-blue-600" />
                  Request New Group
                </CardTitle>
                <Button
                  onClick={onClose}
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </CardHeader>

            <CardContent className="space-y-6">
              {/* Location Info */}
              {selectedLocation && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-start gap-3">
                    <MapPin className="w-5 h-5 text-blue-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-blue-900 mb-1">Selected Location</h4>
                      <p className="text-sm text-blue-700">{selectedLocation.address}</p>
                      <div className="flex gap-4 mt-2">
                        <Badge variant="secondary" className="text-xs">
                          Lat: {selectedLocation.latitude.toFixed(6)}
                        </Badge>
                        <Badge variant="secondary" className="text-xs">
                          Lng: {selectedLocation.longitude.toFixed(6)}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Group Name */}
                <div className="space-y-2">
                  <Label htmlFor="groupName" className="flex items-center gap-2">
                    <Users className="w-4 h-4" />
                    Group Name *
                  </Label>
                  <Input
                    id="groupName"
                    type="text"
                    placeholder="Enter group name (e.g., Soweto Savers)"
                    value={formData.groupName}
                    onChange={(e) => handleFieldChange('groupName', e.target.value)}
                    className={errors.groupName ? 'border-red-500' : ''}
                  />
                  {errors.groupName && (
                    <p className="text-sm text-red-600">{errors.groupName}</p>
                  )}
                </div>

                {/* Description */}
                <div className="space-y-2">
                  <Label htmlFor="description" className="flex items-center gap-2">
                    <FileText className="w-4 h-4" />
                    Description (Optional)
                  </Label>
                  <Textarea
                    id="description"
                    placeholder="Describe your group's purpose and goals..."
                    value={formData.description}
                    onChange={(e) => handleFieldChange('description', e.target.value)}
                    rows={3}
                    className={errors.description ? 'border-red-500' : ''}
                  />
                  {errors.description && (
                    <p className="text-sm text-red-600">{errors.description}</p>
                  )}
                  <p className="text-xs text-gray-500">
                    {formData.description.length}/500 characters
                  </p>
                </div>

                {/* User Information */}
                <div className="space-y-4 pt-4 border-t border-gray-200">
                  <h4 className="font-medium text-gray-900">Your Information</h4>
                  
                  {/* Email */}
                  <div className="space-y-2">
                    <Label htmlFor="userEmail" className="flex items-center gap-2">
                      <Mail className="w-4 h-4" />
                      Email Address *
                    </Label>
                    <Input
                      id="userEmail"
                      type="email"
                      placeholder="<EMAIL>"
                      value={formData.userEmail}
                      onChange={(e) => handleFieldChange('userEmail', e.target.value)}
                      className={errors.userEmail ? 'border-red-500' : ''}
                    />
                    {errors.userEmail && (
                      <p className="text-sm text-red-600">{errors.userEmail}</p>
                    )}
                  </div>

                  {/* Name */}
                  <div className="space-y-2">
                    <Label htmlFor="userName" className="flex items-center gap-2">
                      <User className="w-4 h-4" />
                      Full Name *
                    </Label>
                    <Input
                      id="userName"
                      type="text"
                      placeholder="Your full name"
                      value={formData.userName}
                      onChange={(e) => handleFieldChange('userName', e.target.value)}
                      className={errors.userName ? 'border-red-500' : ''}
                    />
                    {errors.userName && (
                      <p className="text-sm text-red-600">{errors.userName}</p>
                    )}
                  </div>

                  {/* Phone */}
                  <div className="space-y-2">
                    <Label htmlFor="userPhone" className="flex items-center gap-2">
                      <Phone className="w-4 h-4" />
                      Phone Number (Optional)
                    </Label>
                    <Input
                      id="userPhone"
                      type="tel"
                      placeholder="+27 ************"
                      value={formData.userPhone}
                      onChange={(e) => handleFieldChange('userPhone', e.target.value)}
                      className={errors.userPhone ? 'border-red-500' : ''}
                    />
                    {errors.userPhone && (
                      <p className="text-sm text-red-600">{errors.userPhone}</p>
                    )}
                  </div>
                </div>

                {/* Submit Buttons */}
                <div className="flex gap-3 pt-4">
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="flex-1 bg-blue-600 hover:bg-blue-700"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                        Submitting...
                      </>
                    ) : (
                      'Submit Request'
                    )}
                  </Button>
                  <Button
                    type="button"
                    onClick={onClose}
                    variant="outline"
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
