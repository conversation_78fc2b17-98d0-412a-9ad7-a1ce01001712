// components/maps/GroupMarker.tsx

"use client";

import { Marker } from 'react-map-gl';
import { MapPin, Users } from 'lucide-react';
import { motion } from 'framer-motion';
import type { StokvelGroup } from '@/types/stokvelgroup';

export interface GroupMarkerProps {
  group: StokvelGroup;
  onClick: () => void;
  isSelected?: boolean;
}

export function GroupMarker({ group, onClick, isSelected = false }: GroupMarkerProps) {
  if (!group.coordinates?.latitude || !group.coordinates?.longitude) {
    return null;
  }

  const memberCount = group.members?.length || 0;
  
  // Determine marker size based on member count
  const getMarkerSize = () => {
    if (memberCount >= 50) return 'large';
    if (memberCount >= 20) return 'medium';
    return 'small';
  };

  const markerSize = getMarkerSize();
  
  const sizeClasses = {
    small: 'w-8 h-8',
    medium: 'w-10 h-10',
    large: 'w-12 h-12'
  };

  const iconSizes = {
    small: 'w-4 h-4',
    medium: 'w-5 h-5',
    large: 'w-6 h-6'
  };

  return (
    <Marker
      longitude={group.coordinates.longitude}
      latitude={group.coordinates.latitude}
      anchor="bottom"
    >
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
        className="relative cursor-pointer"
        onClick={onClick}
      >
        {/* Main Marker */}
        <div
          className={`
            ${sizeClasses[markerSize]}
            ${isSelected 
              ? 'bg-blue-500 ring-4 ring-blue-200' 
              : 'bg-green-500 hover:bg-green-600'
            }
            rounded-full border-4 border-white shadow-lg 
            flex items-center justify-center
            transition-all duration-200
          `}
        >
          <MapPin className={`${iconSizes[markerSize]} text-white`} />
        </div>

        {/* Pointer */}
        <div 
          className={`
            absolute -bottom-1 left-1/2 transform -translate-x-1/2 
            w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent
            ${isSelected ? 'border-t-blue-500' : 'border-t-green-500'}
          `}
        />

        {/* Member Count Badge */}
        {memberCount > 0 && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full min-w-[20px] h-5 flex items-center justify-center px-1 border-2 border-white shadow-sm"
          >
            {memberCount > 99 ? '99+' : memberCount}
          </motion.div>
        )}

        {/* Pulse Animation for Selected */}
        {isSelected && (
          <motion.div
            className="absolute inset-0 rounded-full border-2 border-blue-400"
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.7, 0, 0.7]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        )}
      </motion.div>
    </Marker>
  );
}
