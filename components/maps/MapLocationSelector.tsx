// components/maps/MapLocationSelector.tsx

"use client";

import { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MapPin, ArrowLeft, Users, Plus } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { LocationMap } from './LocationMap';
import { LocationSelector } from './LocationSelector';
import type { StokvelGroup } from '@/types/stokvelgroup';
import type { Township } from '@/types/locations';

export interface MapLocationSelectorProps {
  township: Township;
  onGroupSelect: (group: StokvelGroup) => void;
  onNewGroupRequest: (coordinates: { latitude: number; longitude: number }, address: string) => void;
  onBack: () => void;
  userEmail?: string;
  userName?: string;
  userPhone?: string;
  className?: string;
}

export function MapLocationSelector({
  township,
  onGroupSelect,
  onNewGroupRequest,
  onBack,
  userEmail,
  userName,
  userPhone,
  className = ""
}: MapLocationSelectorProps) {
  const [existingGroups, setExistingGroups] = useState<StokvelGroup[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedView, setSelectedView] = useState<'map' | 'search'>('map');

  // Fetch existing groups for the township
  useEffect(() => {
    const fetchGroups = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const response = await fetch(`/api/stokvel-groups/by-township/${township._id}`);
        if (!response.ok) {
          throw new Error('Failed to fetch groups');
        }
        
        const data = await response.json();
        setExistingGroups(data.groups || []);
      } catch (error) {
        console.error('Error fetching groups:', error);
        setError('Failed to load groups. Please try again.');
        setExistingGroups([]);
      } finally {
        setIsLoading(false);
      }
    };

    if (township._id) {
      fetchGroups();
    }
  }, [township._id]);

  // Handle location selection from search
  const handleLocationSelect = useCallback((coordinates: { latitude: number; longitude: number }, address: string) => {
    onNewGroupRequest(coordinates, address);
  }, [onNewGroupRequest]);

  if (isLoading) {
    return (
      <Card className={`w-full ${className}`}>
        <CardContent className="p-8">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent" />
            <p className="text-gray-600">Loading groups in {township.name}...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={`w-full ${className}`}>
        <CardContent className="p-8">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
              <MapPin className="w-6 h-6 text-red-600" />
            </div>
            <div className="text-center">
              <h3 className="font-semibold text-gray-900 mb-2">Unable to Load Groups</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={() => window.location.reload()} variant="outline">
                Try Again
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`w-full space-y-4 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                onClick={onBack}
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
              >
                <ArrowLeft className="w-4 h-4" />
              </Button>
              <div>
                <CardTitle className="text-lg flex items-center gap-2">
                  <MapPin className="w-5 h-5 text-blue-600" />
                  Groups in {township.name}
                </CardTitle>
                <p className="text-sm text-gray-600 mt-1">
                  {existingGroups.length > 0 
                    ? `${existingGroups.length} group${existingGroups.length === 1 ? '' : 's'} available`
                    : 'No groups found - be the first to create one!'
                  }
                </p>
              </div>
            </div>
            
            {/* View Toggle */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              <Button
                onClick={() => setSelectedView('map')}
                variant={selectedView === 'map' ? 'default' : 'ghost'}
                size="sm"
                className="h-8"
              >
                Map
              </Button>
              <Button
                onClick={() => setSelectedView('search')}
                variant={selectedView === 'search' ? 'default' : 'ghost'}
                size="sm"
                className="h-8"
              >
                Search
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Content */}
      <AnimatePresence mode="wait">
        {selectedView === 'map' ? (
          <motion.div
            key="map"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.3 }}
          >
            <LocationMap
              township={township}
              existingGroups={existingGroups}
              onGroupSelect={onGroupSelect}
              onNewGroupRequest={onNewGroupRequest}
              userEmail={userEmail}
              userName={userName}
              userPhone={userPhone}
            />
          </motion.div>
        ) : (
          <motion.div
            key="search"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="grid grid-cols-1 lg:grid-cols-2 gap-4"
          >
            {/* Location Search */}
            <LocationSelector
              onLocationSelect={handleLocationSelect}
            />

            {/* Existing Groups List */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Users className="w-5 h-5 text-green-600" />
                  Available Groups
                </CardTitle>
              </CardHeader>
              <CardContent>
                {existingGroups.length > 0 ? (
                  <div className="space-y-3 max-h-80 overflow-y-auto">
                    {existingGroups.map((group) => (
                      <motion.div
                        key={group._id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-all duration-200 cursor-pointer"
                        onClick={() => onGroupSelect(group)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900 mb-1">{group.name}</h4>
                            <p className="text-sm text-gray-600 mb-2">{group.description}</p>
                            <div className="flex items-center gap-2">
                              <Badge variant="secondary" className="text-xs">
                                <Users className="w-3 h-3 mr-1" />
                                {group.members?.length || 0} members
                              </Badge>
                              {group.address && (
                                <Badge variant="outline" className="text-xs">
                                  <MapPin className="w-3 h-3 mr-1" />
                                  {group.address.split(',')[0]}
                                </Badge>
                              )}
                            </div>
                          </div>
                          <Button
                            onClick={(e) => {
                              e.stopPropagation();
                              onGroupSelect(group);
                            }}
                            size="sm"
                            className="bg-green-600 hover:bg-green-700"
                          >
                            Join
                          </Button>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="w-8 h-8 text-gray-400" />
                    </div>
                    <h4 className="font-semibold text-gray-900 mb-2">No Groups Yet</h4>
                    <p className="text-sm text-gray-600 mb-4">
                      Be the first to create a group in {township.name}!
                    </p>
                    <Button
                      onClick={() => setSelectedView('map')}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Create New Group
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Instructions */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <MapPin className="w-4 h-4 text-blue-600" />
            </div>
            <div className="text-sm">
              <p className="text-blue-900 font-medium mb-1">How to find or create a group:</p>
              <ul className="text-blue-700 space-y-1">
                <li>• <strong>Map View:</strong> See groups on the map and click to select a location for a new group</li>
                <li>• <strong>Search View:</strong> Search for specific addresses or browse available groups</li>
                <li>• <strong>Join Existing:</strong> Click on any group to join immediately</li>
                <li>• <strong>Create New:</strong> Select a location to request a new group</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
