"use client"

import { Suspense, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/context/AuthContext"
import UserSignup from "@/components/auth/UserSignup"
import { LoadingScreen } from "@/components/ui/loading-screen"

function RegisterPageContent() {
  const { user, isAuthenticated, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // If user is already authenticated, redirect them away from register page
    if (isAuthenticated && user) {
      console.log("User already authenticated, redirecting from register page")

      // Redirect based on user role
      if (user.role === 'admin') {
        router.push('/admin')
      } else {
        // For customers, redirect to groups page to join a group
        router.push('/groups')
      }
    }
  }, [isAuthenticated, user, router])

  // Show loading screen while checking authentication
  if (loading) {
    return <LoadingScreen />
  }

  // If user is authenticated, don't render the register form
  if (isAuthenticated && user) {
    return <LoadingScreen />
  }

  return <UserSignup />
}

export default function RegisterPage() {
  return (
    <div className="min-h-screen bg-background">
      <Suspense fallback={
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        </div>
      }>
        <RegisterPageContent />
      </Suspense>
    </div>
  )
}

