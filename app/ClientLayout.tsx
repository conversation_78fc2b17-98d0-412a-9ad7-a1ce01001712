// app/ClientLayout.tsx
"use client"

import { usePathname } from "next/navigation"
import { SiteHeader } from "@/components/navigation/site-header"
import { SiteFooter } from "@/components/navigation/site-footer"

export function ClientLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()

  // Check if this is a group dashboard page (not public group pages)
  const isGroupDashboard = pathname.startsWith("/group/") ||
                           (pathname.startsWith("/groups/") && !pathname.startsWith("/groups/join"))

  const shouldShowHeaderAndFooter =
    !pathname.startsWith("/admin") &&
    !pathname.startsWith("/profile") &&
    !isGroupDashboard &&
    pathname !== "/group" // Exclude exact /group path

  return (
    <>
      {shouldShowHeaderAndFooter && <SiteHeader />}
      <main>{children}</main>
      {shouldShowHeaderAndFooter && <SiteFooter />}
    </>
  )
}