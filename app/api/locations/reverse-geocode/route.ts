// app/api/locations/reverse-geocode/route.ts

import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { latitude, longitude } = body;

    if (!latitude || !longitude) {
      return NextResponse.json(
        { error: 'Latitude and longitude are required' },
        { status: 400 }
      );
    }

    // Validate coordinates
    if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
      return NextResponse.json(
        { error: 'Invalid coordinates' },
        { status: 400 }
      );
    }

    const mapboxToken = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN;
    
    if (!mapboxToken) {
      return NextResponse.json(
        { error: 'Mapbox configuration not available' },
        { status: 500 }
      );
    }

    // Call Mapbox Geocoding API for reverse geocoding
    const response = await fetch(
      `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=${mapboxToken}&types=address,poi&country=ZA`
    );

    if (!response.ok) {
      throw new Error(`Mapbox API error: ${response.status}`);
    }

    const data = await response.json();
    
    if (!data.features || data.features.length === 0) {
      return NextResponse.json({
        success: true,
        address: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`,
        formattedAddress: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`,
        locality: null,
        coordinates: { latitude, longitude }
      });
    }

    const feature = data.features[0];
    const address = feature.place_name;
    
    // Extract locality information
    let locality = null;
    if (feature.context) {
      const localityContext = feature.context.find((ctx: any) => 
        ctx.id.startsWith('locality') || ctx.id.startsWith('place')
      );
      locality = localityContext?.text || null;
    }

    return NextResponse.json({
      success: true,
      address,
      formattedAddress: address,
      locality,
      coordinates: { latitude, longitude },
      mapboxData: {
        place_name: feature.place_name,
        center: feature.center,
        place_type: feature.place_type,
        relevance: feature.relevance
      }
    });

  } catch (error) {
    console.error('Error in reverse geocoding:', error);
    return NextResponse.json(
      { 
        error: 'Failed to reverse geocode location',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
