// app/api/group-requests/[id]/reject/route.ts

import { NextRequest, NextResponse } from "next/server";
import mongoose from "mongoose";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import { verifyAccessToken } from "@/lib/auth";
import { GroupRequest } from "@/models/GroupRequest";
import { User } from "@/models/User";
import { NotificationService } from "@/lib/services/notificationService";
import '@/models'; // Ensure all models are loaded

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

// POST /api/group-requests/[id]/reject - Reject group request
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    // Verify authentication (support both web and mobile clients)
    const clientType = req.headers.get('x-client-type') || 'web';
    let accessToken: string | undefined;

    if (clientType === 'web') {
      // For web clients, read from the accessToken cookie
      accessToken = req.cookies.get('accessToken')?.value;
    } else {
      // For mobile clients, read from the Authorization header
      const authHeader = req.headers.get('authorization') || '';
      if (authHeader.startsWith('Bearer ')) {
        accessToken = authHeader.substring(7);
      }
    }

    if (!accessToken) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const payload = await verifyAccessToken(accessToken);
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { headers: corsHeaders, status: 401 }
      );
    }

    // Verify admin role
    const adminUser = await User.findById(payload.id);
    if (!adminUser || adminUser.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { headers: corsHeaders, status: 403 }
      );
    }

    const { id } = await params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid request ID' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const body = await req.json();
    const { reviewNotes } = body;

    // Validate that review notes are provided for rejection
    if (!reviewNotes || reviewNotes.trim().length === 0) {
      return NextResponse.json(
        { error: 'Review notes are required when rejecting a request' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Find the group request
    const groupRequest = await GroupRequest.findById(id);
    if (!groupRequest) {
      return NextResponse.json(
        { error: 'Group request not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Check if already processed
    if (groupRequest.status !== 'pending') {
      return NextResponse.json(
        { error: `Request already ${groupRequest.status}` },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Update the group request status
    groupRequest.status = 'rejected';
    groupRequest.reviewDate = new Date();
    groupRequest.reviewedBy = adminUser._id;
    groupRequest.reviewNotes = reviewNotes.trim();

    await groupRequest.save();

    // Populate the response data
    await groupRequest.populate([
      { path: 'userId', select: 'name email phone' },
      { path: 'reviewedBy', select: 'name email' }
    ]);

    // Send notification to user about rejection
    try {
      await NotificationService.notifyGroupRequestRejected(groupRequest._id.toString());
    } catch (notificationError) {
      console.error('Failed to send rejection notification:', notificationError);
      // Don't fail the request if notification fails
    }

    return NextResponse.json({
      success: true,
      message: 'Group request rejected successfully',
      request: groupRequest
    }, { headers: corsHeaders, status: 200 });

  } catch (error) {
    console.error("Error rejecting group request:", error);
    
    if (error instanceof mongoose.Error.ValidationError) {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return NextResponse.json(
        { error: 'Validation failed', details: validationErrors },
        { headers: corsHeaders, status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal Server Error" },
      { headers: corsHeaders, status: 500 }
    );
  }
}
