// app/api/group-requests/[id]/route.ts

import { NextRequest, NextResponse } from "next/server";
import mongoose from "mongoose";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import { verifyAccessToken } from "@/lib/auth";
import { GroupRequest } from "@/models/GroupRequest";
import { User } from "@/models/User";
import '@/models'; // Ensure all models are loaded

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

// GET /api/group-requests/[id] - Get specific group request
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    // Verify authentication
    // Verify authentication (support both web and mobile clients)
    const clientType = req.headers.get('x-client-type') || 'web';
    let accessToken: string | undefined;

    if (clientType === 'web') {
      // For web clients, read from the accessToken cookie
      accessToken = req.cookies.get('accessToken')?.value;
    } else {
      // For mobile clients, read from the Authorization header
      const authHeader = req.headers.get('authorization') || '';
      if (authHeader.startsWith('Bearer ')) {
        accessToken = authHeader.substring(7);
      }
    }

    if (!accessToken) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const payload = await verifyAccessToken(accessToken);
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const { id } = await params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid request ID' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const groupRequest = await GroupRequest.findById(id)
      .populate('userId', 'name email phone')
      .populate('reviewedBy', 'name email')
      .populate('createdGroupId', 'name _id');

    if (!groupRequest) {
      return NextResponse.json(
        { error: 'Group request not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Get user to check permissions
    const user = await User.findById(payload.id);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Check permissions - user can only see their own requests, admin can see all
    if (user.role !== 'admin' && groupRequest.userId.toString() !== user._id.toString()) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { headers: corsHeaders, status: 403 }
      );
    }

    return NextResponse.json({
      request: groupRequest
    }, { headers: corsHeaders, status: 200 });

  } catch (error) {
    console.error("Error fetching group request:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { headers: corsHeaders, status: 500 }
    );
  }
}

// PUT /api/group-requests/[id] - Update group request (admin only)
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    // Verify authentication (support both web and mobile clients)
    const clientType = req.headers.get('x-client-type') || 'web';
    let accessToken: string | undefined;

    if (clientType === 'web') {
      // For web clients, read from the accessToken cookie
      accessToken = req.cookies.get('accessToken')?.value;
    } else {
      // For mobile clients, read from the Authorization header
      const authHeader = req.headers.get('authorization') || '';
      if (authHeader.startsWith('Bearer ')) {
        accessToken = authHeader.substring(7);
      }
    }

    if (!accessToken) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const payload = await verifyAccessToken(accessToken);
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { headers: corsHeaders, status: 401 }
      );
    }

    // Verify admin role
    const user = await User.findById(payload.id);
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { headers: corsHeaders, status: 403 }
      );
    }

    const { id } = await params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid request ID' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const body = await req.json();
    const { status, reviewNotes } = body;

    // Validate status
    if (status && !['pending', 'approved', 'rejected'].includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const groupRequest = await GroupRequest.findById(id);
    if (!groupRequest) {
      return NextResponse.json(
        { error: 'Group request not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Update fields
    if (status) {
      groupRequest.status = status;
      if (status === 'approved' || status === 'rejected') {
        groupRequest.reviewDate = new Date();
        groupRequest.reviewedBy = user._id;
      }
    }

    if (reviewNotes !== undefined) {
      groupRequest.reviewNotes = reviewNotes;
    }

    await groupRequest.save();

    // Populate the response
    await groupRequest.populate([
      { path: 'userId', select: 'name email phone' },
      { path: 'reviewedBy', select: 'name email' },
      { path: 'createdGroupId', select: 'name _id' }
    ]);

    return NextResponse.json({
      success: true,
      message: 'Group request updated successfully',
      request: groupRequest
    }, { headers: corsHeaders, status: 200 });

  } catch (error) {
    console.error("Error updating group request:", error);
    
    if (error instanceof mongoose.Error.ValidationError) {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return NextResponse.json(
        { error: 'Validation failed', details: validationErrors },
        { headers: corsHeaders, status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal Server Error" },
      { headers: corsHeaders, status: 500 }
    );
  }
}

// DELETE /api/group-requests/[id] - Delete group request (admin only)
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    // Verify authentication (support both web and mobile clients)
    const clientType = req.headers.get('x-client-type') || 'web';
    let accessToken: string | undefined;

    if (clientType === 'web') {
      // For web clients, read from the accessToken cookie
      accessToken = req.cookies.get('accessToken')?.value;
    } else {
      // For mobile clients, read from the Authorization header
      const authHeader = req.headers.get('authorization') || '';
      if (authHeader.startsWith('Bearer ')) {
        accessToken = authHeader.substring(7);
      }
    }

    if (!accessToken) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const payload = await verifyAccessToken(accessToken);
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { headers: corsHeaders, status: 401 }
      );
    }

    // Verify admin role
    const user = await User.findById(payload.id);
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { headers: corsHeaders, status: 403 }
      );
    }

    const { id } = await params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid request ID' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const groupRequest = await GroupRequest.findById(id);
    if (!groupRequest) {
      return NextResponse.json(
        { error: 'Group request not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Don't allow deletion of approved requests that have created groups
    if (groupRequest.status === 'approved' && groupRequest.createdGroupId) {
      return NextResponse.json(
        { error: 'Cannot delete approved request with created group' },
        { headers: corsHeaders, status: 400 }
      );
    }

    await GroupRequest.findByIdAndDelete(id);

    return NextResponse.json({
      success: true,
      message: 'Group request deleted successfully'
    }, { headers: corsHeaders, status: 200 });

  } catch (error) {
    console.error("Error deleting group request:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { headers: corsHeaders, status: 500 }
    );
  }
}
