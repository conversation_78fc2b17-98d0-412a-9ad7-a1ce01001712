// app/api/group-requests/with-coordinates/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { GroupRequest } from '@/models/GroupRequest';
import { Province } from '@/models/Province';
import { City } from '@/models/City';
import { Township } from '@/models/Township';
import { Location } from '@/models/Location';
import { User } from '@/models/User';
import bcrypt from 'bcryptjs';

export async function POST(request: NextRequest) {
  try {
    await connectToDatabase();
    
    const body = await request.json();
    const {
      groupName,
      description,
      coordinates,
      address,
      userEmail,
      userName,
      userPhone,
      userPassword,
      townshipId,
      // Location hierarchy for context
      provinceId,
      provinceName,
      cityId,
      cityName,
      townshipName
    } = body;

    // Validate required fields
    if (!groupName || !coordinates || !userEmail || !userName || !townshipId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate coordinates
    if (!coordinates.latitude || !coordinates.longitude) {
      return NextResponse.json(
        { error: 'Valid coordinates are required' },
        { status: 400 }
      );
    }

    if (coordinates.latitude < -90 || coordinates.latitude > 90 || 
        coordinates.longitude < -180 || coordinates.longitude > 180) {
      return NextResponse.json(
        { error: 'Invalid coordinate values' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userEmail)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Get location hierarchy if not provided
    let locationHierarchy = {
      provinceId,
      provinceName,
      cityId,
      cityName,
      townshipId,
      townshipName
    };

    if (!provinceId || !cityId) {
      // Fetch the complete hierarchy
      const township = await Township.findById(townshipId)
        .populate({
          path: 'cityId',
          populate: {
            path: 'provinceId'
          }
        });

      if (!township) {
        return NextResponse.json(
          { error: 'Township not found' },
          { status: 404 }
        );
      }

      const city = township.cityId as any;
      const province = city.provinceId as any;

      locationHierarchy = {
        provinceId: province._id,
        provinceName: province.name,
        cityId: city._id,
        cityName: city.name,
        townshipId: township._id,
        townshipName: township.name
      };
    }

    // Check if user exists
    let userId = null;
    const existingUser = await User.findOne({ email: userEmail.toLowerCase() });
    
    if (existingUser) {
      userId = existingUser._id;
    } else if (userPassword) {
      // Create new user if password provided
      const hashedPassword = await bcrypt.hash(userPassword, 12);
      const newUser = new User({
        name: userName,
        email: userEmail.toLowerCase(),
        phone: userPhone,
        password: hashedPassword,
        role: 'customer'
      });
      
      const savedUser = await newUser.save();
      userId = savedUser._id;
    }

    // Create or find location for the coordinates
    let locationId = null;
    let locationName = address || `${coordinates.latitude.toFixed(6)}, ${coordinates.longitude.toFixed(6)}`;

    // Try to find existing location near these coordinates (within ~100m)
    const nearbyLocations = await Location.find({
      townshipId: locationHierarchy.townshipId,
      coordinates: {
        $near: {
          $geometry: {
            type: "Point",
            coordinates: [coordinates.longitude, coordinates.latitude]
          },
          $maxDistance: 100 // 100 meters
        }
      }
    });

    if (nearbyLocations.length > 0) {
      locationId = nearbyLocations[0]._id;
      locationName = nearbyLocations[0].name;
    } else {
      // Create new location
      const newLocation = new Location({
        name: locationName,
        townshipId: locationHierarchy.townshipId,
        coordinates: {
          latitude: coordinates.latitude,
          longitude: coordinates.longitude
        },
        address,
        isActive: true
      });
      
      const savedLocation = await newLocation.save();
      locationId = savedLocation._id;
    }

    // Create group request
    const groupRequest = new GroupRequest({
      userId,
      userEmail: userEmail.toLowerCase(),
      userName,
      userPhone,
      userPassword: userPassword ? await bcrypt.hash(userPassword, 12) : undefined,
      
      // Location hierarchy
      provinceId: locationHierarchy.provinceId,
      provinceName: locationHierarchy.provinceName,
      cityId: locationHierarchy.cityId,
      cityName: locationHierarchy.cityName,
      townshipId: locationHierarchy.townshipId,
      townshipName: locationHierarchy.townshipName,
      locationId,
      locationName,
      
      // Group details
      requestedGroupName: groupName,
      groupDescription: description,
      
      // Coordinates and address
      coordinates: {
        latitude: coordinates.latitude,
        longitude: coordinates.longitude
      },
      address,
      
      // Request status
      status: 'pending',
      requestDate: new Date()
    });

    const savedRequest = await groupRequest.save();

    return NextResponse.json({
      success: true,
      message: 'Group request submitted successfully',
      groupRequest: {
        _id: savedRequest._id,
        groupName: savedRequest.requestedGroupName,
        status: savedRequest.status,
        coordinates: savedRequest.coordinates,
        address: savedRequest.address,
        location: {
          province: savedRequest.provinceName,
          city: savedRequest.cityName,
          township: savedRequest.townshipName,
          location: savedRequest.locationName
        }
      },
      user: userId ? {
        _id: userId,
        isNewUser: !existingUser
      } : null
    });

  } catch (error) {
    console.error('Error creating group request with coordinates:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create group request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
