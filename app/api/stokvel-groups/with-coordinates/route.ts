// app/api/stokvel-groups/with-coordinates/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { StokvelGroup } from '@/models/StokvelGroup';
import { Location } from '@/models/Location';

export async function GET(request: NextRequest) {
  try {
    await connectToDatabase();
    
    const { searchParams } = new URL(request.url);
    const includeAll = searchParams.get('includeAll') === 'true';
    const provinceId = searchParams.get('provinceId');
    const cityId = searchParams.get('cityId');
    const townshipId = searchParams.get('townshipId');

    // Build query for location filtering
    let locationQuery: any = {};
    if (townshipId) {
      locationQuery.townshipId = townshipId;
    } else if (cityId) {
      // Get all townships in the city first
      const { Township } = await import('@/models/Township');
      const townships = await Township.find({ cityId }).select('_id');
      locationQuery.townshipId = { $in: townships.map(t => t._id) };
    } else if (provinceId) {
      // Get all cities in the province, then townships
      const { City } = await import('@/models/City');
      const { Township } = await import('@/models/Township');
      const cities = await City.find({ provinceId }).select('_id');
      const townships = await Township.find({ cityId: { $in: cities.map(c => c._id) } }).select('_id');
      locationQuery.townshipId = { $in: townships.map(t => t._id) };
    }

    // Get locations based on filter
    const locations = Object.keys(locationQuery).length > 0 
      ? await Location.find(locationQuery).select('_id name coordinates address')
      : await Location.find({}).select('_id name coordinates address');

    const locationIds = locations.map(loc => loc._id);

    // Build group query
    let groupQuery: any = {};
    if (locationIds.length > 0) {
      groupQuery.locationId = { $in: locationIds };
    }

    // Get groups
    const groups = await StokvelGroup.find(groupQuery)
      .populate('locationId', 'name coordinates address townshipId')
      .populate('admin', 'name email')
      .populate({
        path: 'locationId',
        populate: {
          path: 'townshipId',
          populate: {
            path: 'cityId',
            populate: {
              path: 'provinceId'
            }
          }
        }
      })
      .select('name description members admin locationId coordinates address totalSales avgOrderValue activeOrders bulkOrderThreshold pendingOrderAmount deliveryStatus createdAt updatedAt')
      .lean();

    // Enhance groups with denormalized data
    const enhancedGroups = groups.map(group => {
      const location = group.locationId as any;
      const township = location?.townshipId as any;
      const city = township?.cityId as any;
      const province = city?.provinceId as any;
      
      return {
        ...group,
        coordinates: group.coordinates || location?.coordinates || null,
        address: group.address || location?.address || location?.name || null,
        memberCount: group.members?.length || 0,
        location: location ? {
          _id: location._id,
          name: location.name,
          coordinates: location.coordinates,
          address: location.address,
          hierarchy: {
            province: province ? { _id: province._id, name: province.name, code: province.code } : null,
            city: city ? { _id: city._id, name: city.name } : null,
            township: township ? { _id: township._id, name: township.name } : null,
            location: { _id: location._id, name: location.name }
          }
        } : null
      };
    });

    // Filter groups with coordinates if not including all
    const result = includeAll 
      ? enhancedGroups 
      : enhancedGroups.filter(group => 
          group.coordinates?.latitude && group.coordinates?.longitude
        );

    // Calculate statistics
    const stats = {
      totalGroups: enhancedGroups.length,
      groupsWithCoordinates: enhancedGroups.filter(g => g.coordinates?.latitude && g.coordinates?.longitude).length,
      totalMembers: enhancedGroups.reduce((sum, group) => sum + (group.memberCount || 0), 0),
      activeGroups: enhancedGroups.filter(g => (g.memberCount || 0) > 0).length,
      averageMembersPerGroup: enhancedGroups.length > 0 
        ? Math.round(enhancedGroups.reduce((sum, group) => sum + (group.memberCount || 0), 0) / enhancedGroups.length)
        : 0
    };

    return NextResponse.json({
      success: true,
      groups: result,
      stats,
      filters: {
        provinceId,
        cityId,
        townshipId,
        includeAll
      }
    });

  } catch (error) {
    console.error('Error fetching groups with coordinates:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch groups',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
