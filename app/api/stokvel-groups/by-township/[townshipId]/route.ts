// app/api/stokvel-groups/by-township/[townshipId]/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { StokvelGroup } from '@/models/StokvelGroup';
import { Location } from '@/models/Location';

export async function GET(
  request: NextRequest,
  { params }: { params: { townshipId: string } }
) {
  try {
    await connectToDatabase();
    
    const { townshipId } = params;
    
    if (!townshipId) {
      return NextResponse.json(
        { error: 'Township ID is required' },
        { status: 400 }
      );
    }

    // First, get all locations in the township
    const locations = await Location.find({ 
      townshipId,
      isActive: true 
    }).select('_id name coordinates address');

    const locationIds = locations.map(loc => loc._id);

    // Then get all groups in those locations
    const groups = await StokvelGroup.find({
      locationId: { $in: locationIds }
    })
    .populate('locationId', 'name coordinates address')
    .populate('admin', 'name email')
    .select('name description members admin locationId coordinates address totalSales avgOrderValue activeOrders bulkOrderThreshold pendingOrderAmount deliveryStatus createdAt updatedAt')
    .lean();

    // Enhance groups with denormalized coordinates and address from location if not present
    const enhancedGroups = groups.map(group => {
      const location = group.locationId as any;
      
      return {
        ...group,
        coordinates: group.coordinates || location?.coordinates || null,
        address: group.address || location?.address || location?.name || null,
        memberCount: group.members?.length || 0,
        location: location ? {
          _id: location._id,
          name: location.name,
          coordinates: location.coordinates,
          address: location.address
        } : null
      };
    });

    // Filter out groups without coordinates for map display
    const groupsWithCoordinates = enhancedGroups.filter(group => 
      group.coordinates?.latitude && group.coordinates?.longitude
    );

    return NextResponse.json({
      success: true,
      groups: enhancedGroups,
      groupsWithCoordinates,
      township: {
        _id: townshipId,
        locationCount: locations.length,
        groupCount: groups.length,
        groupsWithCoordinatesCount: groupsWithCoordinates.length
      }
    });

  } catch (error) {
    console.error('Error fetching groups by township:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch groups',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
