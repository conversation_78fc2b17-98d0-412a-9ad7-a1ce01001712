// app/api/shopping-cart/get/route.ts
import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { getShoppingCartByUserId } from '@/lib/shoppingCartBackendUtilities';
import '@/models'; // Ensure all models are loaded

connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    const userId = req.nextUrl.searchParams.get('userId');
    const groupId = req.nextUrl.searchParams.get('groupId');

    console.log(`GET /api/shopping-cart/get - userId: ${userId}, groupId: ${groupId || 'undefined'}`);

    if (!userId || userId.trim() === '') {
      console.log('GET /api/shopping-cart/get - Missing or empty userId');
      return NextResponse.json(
        { error: 'User ID is required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Validate userId format (should be a valid ObjectId)
    if (!/^[0-9a-fA-F]{24}$/.test(userId)) {
      console.log(`GET /api/shopping-cart/get - Invalid userId format: ${userId}`);
      return NextResponse.json(
        { error: 'Invalid User ID format.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Validate groupId if provided - only pass valid ObjectIds or undefined
    const validGroupId = groupId && groupId !== 'default-group' && /^[0-9a-fA-F]{24}$/.test(groupId)
      ? groupId
      : undefined;

    if (groupId && groupId !== 'default-group' && !validGroupId) {
      console.log(`GET /api/shopping-cart/get - Invalid groupId format: ${groupId}`);
    }

    const cart = await getShoppingCartByUserId(userId, validGroupId);
    if (!cart) {
      console.log(`GET /api/shopping-cart/get - Cart not found for user: ${userId}, returning empty cart`);
      // Return an empty cart instead of 404 to prevent errors
      const emptyCart = {
        _id: '',
        userId,
        groupId: validGroupId || '',
        items: [],
        total: 0,
        isFinalized: false
      };
      return NextResponse.json(emptyCart, { headers: corsHeaders, status: 200 });
    }
    console.log(`GET /api/shopping-cart/get - Cart found with ${cart.items.length} items`);
    return NextResponse.json(cart, { headers: corsHeaders, status: 200 });

  } catch (error) {
    console.error('Failed to fetch ShoppingCart:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch ShoppingCart',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}

