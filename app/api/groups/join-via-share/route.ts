// app/api/groups/join-via-share/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { verifyAccessToken } from '@/lib/auth';
import { connectToDatabase } from '@/lib/dbconnect';
import { StokvelGroup } from '@/models/StokvelGroup';
import { User } from '@/models/User';
import { Referral } from '@/models/Referral';
import { getCorsHeaders } from '@/lib/cors';
import { PromotionService } from '@/lib/services/promotionService';
import mongoose from 'mongoose';

const promotionService = new PromotionService();

interface JoinGroupViaShareRequest {
  groupId: string;
  userId: string;
  referralCode?: string;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
}

interface JoinGroupViaShareResponse {
  success: boolean;
  message?: string;
  group?: any;
  referralReward?: {
    pointsEarned: number;
    referrerName: string;
  };
  error?: string;
}

export async function POST(request: NextRequest) {
  const corsHeaders = getCorsHeaders(request.headers.get('origin'));

  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json(
        { error: 'No token provided' }, 
        { headers: corsHeaders, status: 401 }
      );
    }

    const payload = await verifyAccessToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid token' }, 
        { headers: corsHeaders, status: 401 }
      );
    }

    await connectToDatabase();

    const body: JoinGroupViaShareRequest = await request.json();
    const { groupId, userId, referralCode, utmSource, utmMedium, utmCampaign } = body;

    if (!groupId || !userId) {
      return NextResponse.json(
        { error: 'Group ID and User ID are required' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Verify user can only join for themselves (or admin override)
    if (payload.userId !== userId && payload.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized to join group for another user' },
        { headers: corsHeaders, status: 403 }
      );
    }

    // Find the group
    const group = await StokvelGroup.findById(groupId).populate('locationHierarchy');
    if (!group) {
      return NextResponse.json(
        { error: 'Group not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Find the user
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Check if user is already a member
    if (group.members.includes(user._id)) {
      return NextResponse.json(
        { 
          success: true,
          message: 'You are already a member of this group',
          group
        },
        { headers: corsHeaders, status: 200 }
      );
    }

    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Add user to group
      group.members.push(user._id);
      await group.save({ session });

      // Add group to user's groups
      user.stokvelGroups.push(group._id);
      await user.save({ session });

      let referralReward = null;

      // Handle referral tracking if referral code provided
      if (referralCode) {
        // Find the referrer
        const referrer = await User.findOne({ referralCode });
        
        if (referrer && referrer._id.toString() !== userId) {
          // Create referral record
          const referral = new Referral({
            referrerId: referrer._id,
            referredUserId: user._id,
            referralCode,
            earnings: []
          });
          await referral.save({ session });

          // Award points to referrer for successful group invitation
          try {
            const referrerReward = await promotionService.earnLoyaltyPoints({
              userId: referrer._id.toString(),
              action: 'group_referral_success',
              additionalData: { 
                referredUserId: userId,
                groupId,
                groupName: group.name,
                utmSource,
                utmMedium,
                utmCampaign
              }
            });

            if (referrerReward.success) {
              referralReward = {
                pointsEarned: referrerReward.pointsEarned || 200,
                referrerName: referrer.name
              };
            }
          } catch (pointsError) {
            console.warn('Could not award referral points:', pointsError);
          }

          // Award welcome bonus to new member
          try {
            await promotionService.earnLoyaltyPoints({
              userId,
              action: 'group_join_via_referral',
              additionalData: { 
                referrerId: referrer._id.toString(),
                groupId,
                groupName: group.name
              }
            });
          } catch (pointsError) {
            console.warn('Could not award welcome bonus:', pointsError);
          }
        }
      }

      await session.commitTransaction();

      const response: JoinGroupViaShareResponse = {
        success: true,
        message: 'Successfully joined the group!',
        group: {
          _id: group._id,
          name: group.name,
          description: group.description,
          memberCount: group.members.length
        },
        referralReward
      };

      return NextResponse.json(response, {
        headers: corsHeaders,
        status: 200
      });

    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }

  } catch (error) {
    console.error('Error in group join via share API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}

// GET endpoint to fetch group info for share link preview
export async function GET(request: NextRequest) {
  const corsHeaders = getCorsHeaders(request.headers.get('origin'));

  try {
    const { searchParams } = new URL(request.url);
    const groupId = searchParams.get('groupId');
    const referralCode = searchParams.get('ref');

    if (!groupId) {
      return NextResponse.json(
        { error: 'Group ID is required' },
        { headers: corsHeaders, status: 400 }
      );
    }

    await connectToDatabase();

    // Find the group with location hierarchy
    const group = await StokvelGroup.findById(groupId)
      .populate('locationHierarchy')
      .populate('admin', 'name');

    if (!group) {
      return NextResponse.json(
        { error: 'Group not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    let referrerInfo = null;
    if (referralCode) {
      const referrer = await User.findOne({ referralCode }).select('name');
      if (referrer) {
        referrerInfo = {
          name: referrer.name,
          referralCode
        };
      }
    }

    const groupInfo = {
      _id: group._id,
      name: group.name,
      description: group.description,
      memberCount: group.members.length,
      adminName: group.admin?.name || 'Unknown',
      location: group.locationHierarchy ? {
        name: group.locationHierarchy.name,
        township: group.locationHierarchy.townshipId?.name,
        city: group.locationHierarchy.townshipId?.cityId?.name,
        province: group.locationHierarchy.townshipId?.cityId?.provinceId?.name
      } : null,
      referrerInfo
    };

    return NextResponse.json({
      success: true,
      group: groupInfo
    }, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error fetching group info:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error'
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}
