"use client"

import { useEffect, Suspense } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/context/AuthContext"
import UserLogin from "@/components/auth/UserLogin"
import { LoadingScreen } from "@/components/ui/loading-screen"

export default function LoginPage() {
  const { user, isAuthenticated, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // If user is already authenticated, redirect them away from login page
    if (isAuthenticated && user) {
      console.log("User already authenticated, redirecting from login page")

      // Redirect based on user role
      if (user.role === 'admin') {
        router.push('/admin')
      } else {
        // For customers, redirect to groups page to join a group
        router.push('/groups')
      }
    }
  }, [isAuthenticated, user, router])

  // Show loading screen while checking authentication
  if (loading) {
    return <LoadingScreen />
  }

  // If user is authenticated, don't render the login form
  if (isAuthenticated && user) {
    return <LoadingScreen />
  }

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <Suspense fallback={<LoadingScreen />}>
        <UserLogin/>
      </Suspense>
    </div>
  )
}

