// app/test-map/page.tsx
// Test page for map functionality

"use client";

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MapPin, Users, Settings } from 'lucide-react';
import { LocationMap } from '@/components/maps/LocationMap';
import { AdminLocationMap } from '@/components/admin/AdminLocationMap';
import type { StokvelGroup } from '@/types/stokvelgroup';
import type { Township } from '@/types/locations';

// Mock data for testing
const mockTownship: Township = {
  _id: "test-township-id",
  name: "Soweto",
  cityId: "test-city-id",
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date()
};

const mockGroups: StokvelGroup[] = [
  {
    _id: "group-1",
    name: "Soweto Savers",
    description: "Community savings group for bulk grocery purchases",
    members: ["user1", "user2", "user3"],
    admin: "user1",
    coordinates: {
      latitude: -26.2678,
      longitude: 27.8546
    },
    address: "Orlando West, Soweto, Johannesburg",
    totalSales: 15000,
    avgOrderValue: 250,
    activeOrders: 3,
    bulkOrderThreshold: 1000,
    pendingOrderAmount: 750,
    deliveryStatus: 'pending',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: "group-2",
    name: "Diepkloof Buyers Club",
    description: "Bulk buying group for household essentials",
    members: ["user4", "user5"],
    admin: "user4",
    coordinates: {
      latitude: -26.2800,
      longitude: 27.8600
    },
    address: "Diepkloof, Soweto, Johannesburg",
    totalSales: 8500,
    avgOrderValue: 180,
    activeOrders: 1,
    bulkOrderThreshold: 800,
    pendingOrderAmount: 320,
    deliveryStatus: 'delivered',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

export default function TestMapPage() {
  const [selectedView, setSelectedView] = useState<'user' | 'admin'>('user');
  const [selectedGroup, setSelectedGroup] = useState<StokvelGroup | null>(null);
  const [newGroupRequests, setNewGroupRequests] = useState<Array<{
    coordinates: { latitude: number; longitude: number };
    address: string;
    timestamp: Date;
  }>>([]);

  const handleGroupSelect = (group: StokvelGroup) => {
    setSelectedGroup(group);
    console.log('Group selected:', group);
  };

  const handleNewGroupRequest = (coordinates: { latitude: number; longitude: number }, address: string) => {
    const request = {
      coordinates,
      address,
      timestamp: new Date()
    };
    setNewGroupRequests(prev => [...prev, request]);
    console.log('New group request:', request);
  };

  const handleGroupEdit = (group: StokvelGroup) => {
    console.log('Edit group:', group);
  };

  const handleGroupDelete = (group: StokvelGroup) => {
    console.log('Delete group:', group);
  };

  const handleGroupView = (group: StokvelGroup) => {
    console.log('View group:', group);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-2xl flex items-center gap-2">
                  <MapPin className="w-6 h-6 text-blue-600" />
                  Map Functionality Test
                </CardTitle>
                <p className="text-gray-600 mt-1">
                  Test the new map-based location and group creation system
                </p>
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={() => setSelectedView('user')}
                  variant={selectedView === 'user' ? 'default' : 'outline'}
                  className="flex items-center gap-2"
                >
                  <Users className="w-4 h-4" />
                  User View
                </Button>
                <Button
                  onClick={() => setSelectedView('admin')}
                  variant={selectedView === 'admin' ? 'default' : 'outline'}
                  className="flex items-center gap-2"
                >
                  <Settings className="w-4 h-4" />
                  Admin View
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <MapPin className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Mock Groups</p>
                  <p className="text-xl font-semibold">{mockGroups.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <Users className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Selected Group</p>
                  <p className="text-xl font-semibold">{selectedGroup ? selectedGroup.name : 'None'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Settings className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">New Requests</p>
                  <p className="text-xl font-semibold">{newGroupRequests.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Map Component */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="w-5 h-5 text-blue-600" />
              {selectedView === 'user' ? 'User Map Interface' : 'Admin Map Interface'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {selectedView === 'user' ? (
              <LocationMap
                township={mockTownship}
                existingGroups={mockGroups}
                onGroupSelect={handleGroupSelect}
                onNewGroupRequest={handleNewGroupRequest}
                userEmail="<EMAIL>"
                userName="Test User"
                userPhone="+27123456789"
              />
            ) : (
              <AdminLocationMap
                onGroupEdit={handleGroupEdit}
                onGroupDelete={handleGroupDelete}
                onGroupView={handleGroupView}
              />
            )}
          </CardContent>
        </Card>

        {/* Selected Group Info */}
        {selectedGroup && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Selected Group Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">{selectedGroup.name}</h4>
                  <p className="text-gray-600 mb-3">{selectedGroup.description}</p>
                  <div className="flex gap-2 mb-3">
                    <Badge variant="secondary">
                      {selectedGroup.members.length} members
                    </Badge>
                    <Badge variant="outline">
                      {selectedGroup.deliveryStatus}
                    </Badge>
                  </div>
                </div>
                <div>
                  <h5 className="font-medium text-gray-900 mb-2">Location</h5>
                  <p className="text-sm text-gray-600 mb-1">{selectedGroup.address}</p>
                  <p className="text-xs text-gray-500">
                    {selectedGroup.coordinates?.latitude.toFixed(6)}, {selectedGroup.coordinates?.longitude.toFixed(6)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* New Group Requests */}
        {newGroupRequests.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">New Group Requests</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {newGroupRequests.map((request, index) => (
                  <div key={index} className="p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-start justify-between">
                      <div>
                        <p className="font-medium text-gray-900">Request #{index + 1}</p>
                        <p className="text-sm text-gray-600">{request.address}</p>
                        <p className="text-xs text-gray-500">
                          {request.coordinates.latitude.toFixed(6)}, {request.coordinates.longitude.toFixed(6)}
                        </p>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {request.timestamp.toLocaleTimeString()}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <h4 className="font-semibold text-blue-900 mb-2">Testing Instructions</h4>
            <div className="text-sm text-blue-800 space-y-1">
              <p>• <strong>User View:</strong> Click on existing group markers to select them, or click on empty areas to create new group requests</p>
              <p>• <strong>Admin View:</strong> View all groups on the map with management controls</p>
              <p>• <strong>Map Controls:</strong> Use zoom, pan, and style toggle controls</p>
              <p>• <strong>Mobile:</strong> Test touch interactions and responsive design</p>
              <p>• <strong>Note:</strong> Requires Mapbox token in environment variables</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
