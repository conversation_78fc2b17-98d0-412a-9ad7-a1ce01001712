"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  Users, 
  MapPin, 
  Star, 
  Gift, 
  ArrowRight,
  CheckCircle,
  Sparkles,
  Heart,
  TrendingUp
} from "lucide-react";
import { motion } from "framer-motion";
import { toast } from "sonner";
import { useAuth } from "@/context/AuthContext";
import Link from "next/link";

interface GroupInfo {
  _id: string;
  name: string;
  description: string;
  memberCount: number;
  adminName: string;
  location?: {
    name: string;
    township?: string;
    city?: string;
    province?: string;
  };
  referrerInfo?: {
    name: string;
    referralCode: string;
  };
}

export default function GroupJoinPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, isLoading: authLoading } = useAuth();
  
  const [groupInfo, setGroupInfo] = useState<GroupInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isJoining, setIsJoining] = useState(false);
  const [hasJoined, setHasJoined] = useState(false);

  const groupId = params.groupId as string;
  const referralCode = searchParams.get('ref');
  const utmSource = searchParams.get('utm_source');
  const utmMedium = searchParams.get('utm_medium');
  const utmCampaign = searchParams.get('utm_campaign');

  useEffect(() => {
    fetchGroupInfo();
  }, [groupId, referralCode]);

  const fetchGroupInfo = async () => {
    try {
      const url = new URL('/api/groups/join-via-share', window.location.origin);
      url.searchParams.set('groupId', groupId);
      if (referralCode) {
        url.searchParams.set('ref', referralCode);
      }

      const response = await fetch(url.toString());
      const data = await response.json();

      if (data.success) {
        setGroupInfo(data.group);
      } else {
        toast.error(data.error || 'Failed to load group information');
      }
    } catch (error) {
      console.error('Error fetching group info:', error);
      toast.error('Failed to load group information');
    } finally {
      setIsLoading(false);
    }
  };

  const handleJoinGroup = async () => {
    if (!user) {
      // Redirect to login with return URL
      const returnUrl = `/groups/join/${groupId}?${searchParams.toString()}`;
      router.push(`/login?returnUrl=${encodeURIComponent(returnUrl)}`);
      return;
    }

    setIsJoining(true);

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/groups/join-via-share', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          groupId,
          userId: user._id,
          referralCode,
          utmSource,
          utmMedium,
          utmCampaign
        })
      });

      const data = await response.json();

      if (data.success) {
        setHasJoined(true);
        toast.success(data.message || 'Successfully joined the group!');
        
        if (data.referralReward) {
          toast.success(
            `🎉 ${data.referralReward.referrerName} earned ${data.referralReward.pointsEarned} points for inviting you!`
          );
        }

        // Redirect to group page after a delay
        setTimeout(() => {
          router.push(`/group/${groupId}`);
        }, 2000);
      } else {
        toast.error(data.error || 'Failed to join group');
      }
    } catch (error) {
      console.error('Error joining group:', error);
      toast.error('Failed to join group');
    } finally {
      setIsJoining(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary/5 to-purple-500/5 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="h-4 w-full" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-10 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!groupInfo) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary/5 to-purple-500/5 flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center">
          <CardContent className="p-8">
            <h2 className="text-xl font-bold text-gray-900 mb-2">
              Group Not Found
            </h2>
            <p className="text-gray-600 mb-4">
              The group you're looking for doesn't exist or has been removed.
            </p>
            <Button asChild>
              <Link href="/">Go Home</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 to-purple-500/5 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="w-full max-w-lg"
      >
        <Card className="overflow-hidden shadow-xl">
          {/* Header */}
          <div className="bg-gradient-to-r from-primary to-purple-600 text-white p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-white/20 rounded-lg">
                <Users className="h-6 w-6" />
              </div>
              <div>
                <h1 className="text-xl font-bold">Join Stokvel Group</h1>
                <p className="text-primary-foreground/80 text-sm">
                  Save money together through bulk buying
                </p>
              </div>
            </div>
          </div>

          <CardContent className="p-6 space-y-6">
            {/* Referrer Info */}
            {groupInfo.referrerInfo && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className="bg-green-50 border border-green-200 rounded-lg p-4"
              >
                <div className="flex items-center gap-2 mb-2">
                  <Heart className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-800">
                    Invited by {groupInfo.referrerInfo.name}
                  </span>
                </div>
                <p className="text-xs text-green-700">
                  🎁 You'll both earn bonus points when you join!
                </p>
              </motion.div>
            )}

            {/* Group Info */}
            <div className="space-y-4">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  {groupInfo.name}
                </h2>
                <p className="text-gray-600">
                  {groupInfo.description}
                </p>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-primary">
                    {groupInfo.memberCount}
                  </div>
                  <div className="text-sm text-gray-600">Members</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    <TrendingUp className="h-6 w-6 mx-auto" />
                  </div>
                  <div className="text-sm text-gray-600">Growing</div>
                </div>
              </div>

              {/* Location */}
              {groupInfo.location && (
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <MapPin className="h-4 w-4" />
                  <span>
                    {[
                      groupInfo.location.name,
                      groupInfo.location.township,
                      groupInfo.location.city,
                      groupInfo.location.province
                    ].filter(Boolean).join(', ')}
                  </span>
                </div>
              )}

              {/* Admin */}
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Star className="h-4 w-4" />
                <span>Managed by {groupInfo.adminName}</span>
              </div>
            </div>

            {/* Benefits */}
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="p-4">
                <h3 className="font-semibold text-blue-900 mb-3 flex items-center gap-2">
                  <Sparkles className="h-4 w-4" />
                  Benefits of Joining
                </h3>
                <ul className="space-y-2 text-sm text-blue-800">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    Save money through bulk purchasing
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    Access to group discounts
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    Community support and collaboration
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    Earn loyalty points and rewards
                  </li>
                </ul>
              </CardContent>
            </Card>

            {/* Action Button */}
            {hasJoined ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center space-y-3"
              >
                <div className="flex items-center justify-center gap-2 text-green-600">
                  <CheckCircle className="h-6 w-6" />
                  <span className="font-semibold">Successfully Joined!</span>
                </div>
                <p className="text-sm text-gray-600">
                  Redirecting to your group dashboard...
                </p>
              </motion.div>
            ) : (
              <Button
                onClick={handleJoinGroup}
                disabled={isJoining || authLoading}
                className="w-full h-12 text-lg"
                size="lg"
              >
                {isJoining ? (
                  "Joining..."
                ) : user ? (
                  <>
                    Join Group
                    <ArrowRight className="h-5 w-5 ml-2" />
                  </>
                ) : (
                  "Login to Join Group"
                )}
              </Button>
            )}

            {!user && (
              <p className="text-xs text-center text-gray-500">
                Don't have an account? You'll be able to create one after clicking above.
              </p>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
