// models/Location.ts

import mongoose, { Schema, Document, model } from 'mongoose';

export interface ILocation extends Document {
  name: string;
  townshipId: mongoose.Types.ObjectId;
  description?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  address?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const LocationSchema: Schema<ILocation> = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: 100
    },
    townshipId: {
      type: Schema.Types.ObjectId,
      ref: 'Township',
      required: true
    },
    description: {
      type: String,
      trim: true,
      maxlength: 500
    },
    coordinates: {
      latitude: {
        type: Number,
        min: -90,
        max: 90
      },
      longitude: {
        type: Number,
        min: -180,
        max: 180
      }
    },
    address: {
      type: String,
      trim: true,
      maxlength: 300
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

// Compound unique index to prevent duplicate location names within the same township
LocationSchema.index({ name: 1, townshipId: 1 }, { unique: true });

// Additional indexes for efficient queries
// Note: name index is covered by compound unique index above
LocationSchema.index({ townshipId: 1 });
LocationSchema.index({ isActive: 1 });
LocationSchema.index({ townshipId: 1, isActive: 1 });
// Geospatial index for coordinate-based queries
LocationSchema.index({ "coordinates": "2dsphere" });

// Virtual for populated township
LocationSchema.virtual('township', {
  ref: 'Township',
  localField: 'townshipId',
  foreignField: '_id',
  justOne: true
});

// Ensure virtual fields are serialized
LocationSchema.set('toJSON', { virtuals: true });
LocationSchema.set('toObject', { virtuals: true });

// Export the model
export const Location = mongoose.models.Location || model<ILocation>('Location', LocationSchema);
