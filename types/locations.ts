// types/locations.ts

/**
 * Frontend-friendly location type definitions for the hierarchical location system
 * Provinces → Cities → Townships → Locations (Groups)
 */

// Base interface for all location entities
export interface BaseLocationEntity {
  _id: string;
  name: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Province interface
export interface Province extends BaseLocationEntity {
  code: string; // e.g., "GP", "WC", "KZN"
}

// City interface
export interface City extends BaseLocationEntity {
  provinceId: string;
  province?: Province; // Populated province data
}

// Township interface
export interface Township extends BaseLocationEntity {
  cityId: string;
  city?: City; // Populated city data (with province)
}

// Location interface (final level where groups are created)
export interface Location extends BaseLocationEntity {
  townshipId: string;
  description?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  address?: string;
  township?: Township; // Populated township data (with city and province)
}

// Complete location hierarchy for a specific location
export interface LocationHierarchy {
  location: Location;
  township: Township;
  city: City;
  province: Province;
}

// Location selection state for forms and wizards
export interface LocationSelectionData {
  selectedProvinceId: string;
  selectedCityId: string;
  selectedTownshipId: string;
  selectedLocationId: string;
  availableProvinces: Province[];
  availableCities: City[];
  availableTownships: Township[];
  availableLocations: Location[];
  isLoading: {
    provinces: boolean;
    cities: boolean;
    townships: boolean;
    locations: boolean;
  };
}

// Search results interface
export interface LocationSearchResults {
  query: string;
  results: {
    provinces: Province[];
    cities: City[];
    townships: Township[];
    locations: Location[];
  };
  totalResults: {
    provinces: number;
    cities: number;
    townships: number;
    locations: number;
  };
}

// Location statistics interface
export interface LocationStats {
  provinces: number;
  cities: number;
  townships: number;
  locations: number;
}

// API response interfaces
export interface ProvinceResponse {
  province?: Province;
  provinces?: Province[];
}

export interface CityResponse {
  city?: City;
  cities?: City[];
}

export interface TownshipResponse {
  township?: Township;
  townships?: Township[];
}

export interface LocationResponse {
  location?: Location;
  locations?: Location[];
}

export interface LocationStatsResponse {
  stats: LocationStats;
  timestamp: string;
}

// Form data interfaces for creating/updating locations
export interface CreateProvinceData {
  name: string;
  code: string;
}

export interface UpdateProvinceData extends Partial<CreateProvinceData> {
  id: string;
  isActive?: boolean;
}

export interface CreateCityData {
  name: string;
  provinceId: string;
}

export interface UpdateCityData extends Partial<Omit<CreateCityData, 'provinceId'>> {
  id: string;
  isActive?: boolean;
}

export interface CreateTownshipData {
  name: string;
  cityId: string;
}

export interface UpdateTownshipData extends Partial<Omit<CreateTownshipData, 'cityId'>> {
  id: string;
  isActive?: boolean;
}

export interface CreateLocationData {
  name: string;
  townshipId: string;
  description?: string;
}

export interface UpdateLocationData extends Partial<Omit<CreateLocationData, 'townshipId'>> {
  id: string;
  isActive?: boolean;
}

// Utility types for location path display
export type LocationPath = {
  province: string;
  city: string;
  township: string;
  location: string;
};

export type LocationBreadcrumb = {
  level: 'province' | 'city' | 'township' | 'location';
  id: string;
  name: string;
  path: string;
};

// Location validation interfaces
export interface LocationValidationError {
  field: string;
  message: string;
}

export interface LocationValidationResult {
  isValid: boolean;
  errors: LocationValidationError[];
}

// Location filter and sorting options
export interface LocationFilterOptions {
  provinceId?: string;
  cityId?: string;
  townshipId?: string;
  isActive?: boolean;
  searchTerm?: string;
}

export interface LocationSortOptions {
  field: 'name' | 'createdAt' | 'updatedAt';
  direction: 'asc' | 'desc';
}

// Pagination interface for location lists
export interface LocationPagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Complete location list response with pagination
export interface PaginatedLocationResponse<T> {
  data: T[];
  pagination: LocationPagination;
  filters?: LocationFilterOptions;
  sort?: LocationSortOptions;
}

// Location dropdown option interface for UI components
export interface LocationOption {
  value: string;
  label: string;
  disabled?: boolean;
  description?: string;
}

// Location tree structure for hierarchical displays
export interface LocationTreeNode {
  id: string;
  name: string;
  type: 'province' | 'city' | 'township' | 'location';
  children?: LocationTreeNode[];
  parent?: string;
  level: number;
  isExpanded?: boolean;
  isSelected?: boolean;
}

// Location management permissions
export interface LocationPermissions {
  canCreate: boolean;
  canRead: boolean;
  canUpdate: boolean;
  canDelete: boolean;
  canManageAll: boolean;
}

// Location audit trail
export interface LocationAuditEntry {
  id: string;
  entityType: 'province' | 'city' | 'township' | 'location';
  entityId: string;
  action: 'create' | 'update' | 'delete' | 'activate' | 'deactivate';
  changes: Record<string, { from: any; to: any }>;
  userId: string;
  userEmail: string;
  timestamp: string;
  ipAddress?: string;
}

export interface LocationAuditResponse {
  entries: LocationAuditEntry[];
  pagination: LocationPagination;
}
