// types/stokvelgroup.ts

import type { LocationHierarchy } from './locations';

/**
 * Mirrors the fields from IStokvelGroup in the Mongoose model,
 * but using front-end-friendly types (e.g., string for _id, etc.).
 */
export interface StokvelGroup {
    _id: string;              // Mongoose assigns string IDs on the front-end
    name: string;
    description: string;
    locationId?: string;      // NEW: Reference to Location (optional during migration)
    geolocation?: string;     // DEPRECATED: Keep for migration period (optional)
    coordinates?: {           // NEW: Denormalized coordinates for performance
        latitude: number;
        longitude: number;
    };
    address?: string;         // NEW: Denormalized address for performance
    members: string[];        // array of user IDs (or user objects if populated)
    admin: string;            // user ID (or user object) for the group admin
    totalSales: number;
    avgOrderValue: number;
    activeOrders: number;
    bulkOrderThreshold: number;
    pendingOrderAmount: number;
    deliveryStatus: 'pending' | 'in-transit' | 'delivered';
    createdAt: string;        // or Date, if you're consistently parsing these
    updatedAt: string;        // or Date
}

/**
 * Extended StokvelGroup interface with populated location hierarchy
 */
export interface StokvelGroupWithLocation extends StokvelGroup {
    locationHierarchy?: LocationHierarchy;
    locationPath?: string;    // Human-readable location path (e.g., "Gauteng > Johannesburg > Soweto > Orlando East")
}

/**
 * StokvelGroup creation data interface
 */
export interface CreateStokvelGroupData {
    name: string;
    description: string;
    admin: string;
    locationId: string;       // Required for new groups
    members?: string[];
    hasDelivery?: boolean;
    totalSales?: number;
    avgOrderValue?: number;
    activeOrders?: number;
}

/**
 * StokvelGroup update data interface
 */
export interface UpdateStokvelGroupData {
    id: string;
    updateData: {
        name?: string;
        description?: string;
        locationId?: string;
        totalSales?: number;
        avgOrderValue?: number;
        activeOrders?: number;
        bulkOrderThreshold?: number;
        pendingOrderAmount?: number;
        deliveryStatus?: 'pending' | 'in-transit' | 'delivered';
    };
}

/**
 * Group joining request interface
 */
export interface JoinGroupRequest {
    userId: string;
    groupId: string;
    isRelocation?: boolean;
}

/**
 * Group search and filter options
 */
export interface GroupFilterOptions {
    locationId?: string;
    provinceId?: string;
    cityId?: string;
    townshipId?: string;
    searchTerm?: string;
    minMembers?: number;
    maxMembers?: number;
    hasActiveOrders?: boolean;
    deliveryStatus?: 'pending' | 'in-transit' | 'delivered';
}

/**
 * Group statistics interface
 */
export interface GroupStats {
    totalGroups: number;
    activeGroups: number;
    totalMembers: number;
    averageMembersPerGroup: number;
    totalSales: number;
    averageSalesPerGroup: number;
    groupsByLocation: {
        locationId: string;
        locationName: string;
        groupCount: number;
        memberCount: number;
    }[];
}

/**
 * Group membership data interface
 */
export interface GroupMembershipData {
    isMember: boolean;
    isMemberOfAnyGroup: boolean;
    group?: StokvelGroupWithLocation;
    membershipDate?: string;
    role?: 'admin' | 'member';
}

/**
 * Location request payload for users without matching groups
 */
export interface LocationRequestPayload {
    location: string;
    userId?: string;
    locationId?: string;    // NEW: Use structured location instead of free text
    provinceId?: string;
    cityId?: string;
    townshipId?: string;
}

/**
 * Group analytics data
 */
export interface GroupAnalytics {
    groupId: string;
    groupName: string;
    memberGrowth: {
        date: string;
        memberCount: number;
    }[];
    salesGrowth: {
        date: string;
        totalSales: number;
    }[];
    orderActivity: {
        date: string;
        orderCount: number;
        orderValue: number;
    }[];
    topProducts: {
        productId: string;
        productName: string;
        orderCount: number;
        totalValue: number;
    }[];
}
  