'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CreditCard, 
  Shield, 
  Zap, 
  AlertCircle, 
  CheckCircle,
  Loader2,
  ArrowRight,
  Lock,
  Globe
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { usePeach } from '../hooks/usePeach';
import { PeachCheckoutProps } from '../types';
import { PeachUtils } from '../utils';
import { toast } from 'sonner';

export function PeachCheckout({
  orderId,
  amount,
  currency = 'ZAR',
  description,
  customerEmail,
  customerName,
  paymentBrand = 'CARD',
  onSuccess,
  onError,
  onCancel,
  className = '',
  disabled = false
}: PeachCheckoutProps) {
  const {
    createAndProcessPayment,
    isLoading,
    error,
    clearCurrentError
  } = usePeach();

  const [isProcessing, setIsProcessing] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  // Clear error when component mounts
  useEffect(() => {
    clearCurrentError();
  }, [clearCurrentError]);

  // Handle error changes
  useEffect(() => {
    if (error) {
      onError?.(error);
      toast.error(error);
    }
  }, [error, onError]);

  const handlePayment = async () => {
    if (disabled || isLoading || isProcessing) return;

    try {
      setIsProcessing(true);
      clearCurrentError();

      const success = await createAndProcessPayment({
        orderId,
        amount,
        currency,
        description,
        customerEmail,
        customerName,
        paymentBrand,
        customData: {
          source: 'StokvelGrocery',
          module: 'PeachCheckout'
        }
      });

      if (success) {
        toast.success('Redirecting to secure payment...');
        onSuccess?.({
          orderId,
          status: 'pending',
          paymentMethod: 'peach',
          paymentBrand,
          amount,
          currency
        });
      } else {
        throw new Error('Failed to create payment');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Payment failed';
      onError?.(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleConfirmPayment = () => {
    setShowConfirmation(false);
    handlePayment();
  };

  // Get payment method configurations
  const paymentMethodConfigs = PeachUtils.getPaymentMethodConfigs();
  const availableMethods = paymentMethodConfigs.filter(method => 
    method.currencies.includes(currency) && method.enabled
  );

  return (
    <div className={`w-full max-w-md mx-auto ${className}`}>
      <AnimatePresence mode="wait">
        {!showConfirmation ? (
          <motion.div
            key="payment-form"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto w-16 h-16 bg-gradient-to-br from-orange-500 to-pink-600 rounded-full flex items-center justify-center mb-4">
                  <CreditCard className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-2xl font-bold text-gray-900">
                  Secure Payment
                </CardTitle>
                <p className="text-gray-600">Powered by Peach Payments</p>
              </CardHeader>

              <CardContent className="space-y-6">
                {/* Amount Display */}
                <div className="text-center p-6 bg-gradient-to-r from-orange-50 to-pink-50 rounded-xl border border-orange-100">
                  <p className="text-3xl font-bold text-gray-900 mb-1">
                    {currency === 'ZAR' ? 'R' : currency} {amount.toFixed(2)}
                  </p>
                  <p className="text-sm text-gray-600">{description}</p>
                </div>

                {/* Payment Methods Grid */}
                <div className="space-y-3">
                  <h4 className="font-semibold text-gray-900 text-sm">
                    Available Payment Methods
                  </h4>
                  <div className="grid grid-cols-2 gap-2">
                    {availableMethods.slice(0, 8).map((method, index) => (
                      <motion.div
                        key={method.id}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.1 }}
                        className={`p-3 border rounded-lg text-center transition-all ${
                          method.id === paymentBrand 
                            ? 'border-orange-300 bg-orange-50' 
                            : 'bg-white border-gray-200 hover:border-orange-200'
                        }`}
                      >
                        <div className="text-2xl mb-1">{method.icon}</div>
                        <p className="text-xs font-medium text-gray-900">{method.name}</p>
                        <p className="text-xs text-gray-500">{method.description}</p>
                        {method.popular && (
                          <Badge className="mt-1 bg-orange-100 text-orange-800 text-xs">
                            Popular
                          </Badge>
                        )}
                      </motion.div>
                    ))}
                  </div>
                  
                  {availableMethods.length > 8 && (
                    <p className="text-xs text-center text-gray-500">
                      +{availableMethods.length - 8} more payment methods available
                    </p>
                  )}
                </div>

                {/* Security Features */}
                <div className="flex items-center justify-center gap-6 text-sm text-gray-600 py-2">
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4 text-green-600" />
                    <span>Bank-level Security</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Zap className="h-4 w-4 text-blue-600" />
                    <span>Instant Processing</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-purple-600" />
                    <span>Multi-currency</span>
                  </div>
                </div>

                {/* Error Display */}
                {error && (
                  <Alert className="border-red-200 bg-red-50">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <AlertDescription className="text-red-800">
                      {error}
                    </AlertDescription>
                  </Alert>
                )}

                {/* Payment Button */}
                <Button
                  onClick={() => setShowConfirmation(true)}
                  disabled={disabled || isLoading || isProcessing}
                  className="w-full h-12 bg-gradient-to-r from-orange-600 to-pink-600 hover:from-orange-700 hover:to-pink-700 text-white font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  {isLoading || isProcessing ? (
                    <>
                      <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      Pay with Peach Payments
                      <ArrowRight className="h-5 w-5 ml-2" />
                    </>
                  )}
                </Button>

                {/* Cancel Button */}
                {onCancel && (
                  <Button
                    variant="outline"
                    onClick={onCancel}
                    disabled={isLoading || isProcessing}
                    className="w-full"
                  >
                    Cancel
                  </Button>
                )}

                {/* Trust Indicators */}
                <div className="text-center space-y-2">
                  <div className="flex items-center justify-center gap-2">
                    <Badge className="bg-green-100 text-green-800 text-xs">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      PCI DSS Compliant
                    </Badge>
                    <Badge className="bg-blue-100 text-blue-800 text-xs">
                      <Lock className="h-3 w-3 mr-1" />
                      256-bit SSL
                    </Badge>
                    <Badge className="bg-purple-100 text-purple-800 text-xs">
                      <Shield className="h-3 w-3 mr-1" />
                      3D Secure
                    </Badge>
                  </div>
                  <p className="text-xs text-gray-500">
                    Your payment is protected by enterprise-grade security
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ) : (
          <motion.div
            key="confirmation"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50">
              <CardHeader className="text-center">
                <div className="mx-auto w-16 h-16 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center mb-4">
                  <CheckCircle className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-xl font-bold text-gray-900">
                  Confirm Payment
                </CardTitle>
              </CardHeader>

              <CardContent className="space-y-6">
                {/* Payment Summary */}
                <div className="p-4 bg-gray-50 rounded-lg space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Amount:</span>
                    <span className="font-semibold">
                      {currency === 'ZAR' ? 'R' : currency} {amount.toFixed(2)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Description:</span>
                    <span className="font-semibold text-right flex-1 ml-2">{description}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Order ID:</span>
                    <span className="font-mono text-sm">{orderId}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Payment Method:</span>
                    <span className="font-semibold">
                      {PeachUtils.getPaymentMethodConfig(paymentBrand)?.name || paymentBrand}
                    </span>
                  </div>
                </div>

                <Alert className="border-blue-200 bg-blue-50">
                  <AlertCircle className="h-4 w-4 text-blue-600" />
                  <AlertDescription className="text-blue-800">
                    You will be redirected to Peach Payments to complete your payment securely.
                  </AlertDescription>
                </Alert>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <Button
                    onClick={handleConfirmPayment}
                    disabled={isLoading || isProcessing}
                    className="w-full h-12 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-semibold"
                  >
                    {isLoading || isProcessing ? (
                      <>
                        <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                        Redirecting...
                      </>
                    ) : (
                      <>
                        Confirm & Pay {currency === 'ZAR' ? 'R' : currency} {amount.toFixed(2)}
                        <ArrowRight className="h-5 w-5 ml-2" />
                      </>
                    )}
                  </Button>

                  <Button
                    variant="outline"
                    onClick={() => setShowConfirmation(false)}
                    disabled={isLoading || isProcessing}
                    className="w-full"
                  >
                    Back to Payment Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
