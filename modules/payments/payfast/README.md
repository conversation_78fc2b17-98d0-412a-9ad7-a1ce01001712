# 🍑 PayFast Payment Module

A complete, modular PayFast payment integration for StokvelGrocery with TypeScript support, Redux state management, and React components.

## 📋 **Overview**

This module provides a comprehensive PayFast integration including:
- ✅ **Complete Type Safety** - Full TypeScript definitions
- ✅ **Redux Integration** - State management with RTK
- ✅ **React Components** - Pre-built UI components
- ✅ **Utility Functions** - Validation, signature generation, etc.
- ✅ **Service Layer** - Clean API abstraction
- ✅ **Hooks** - Custom React hooks for easy integration

## 🚀 **Quick Start**

### **1. Installation**

```bash
# The module is self-contained within the project
# No additional dependencies required beyond existing project deps
```

### **2. Environment Setup**

```bash
# .env.local
PAYFAST_MERCHANT_ID=10000100
PAYFAST_MERCHANT_KEY=46f0cd694581a
PAYFAST_PASSPHRASE=jt7NOE43FZPn
PAYFAST_SANDBOX=true
PAYFAST_RETURN_URL=http://localhost:3001/payment/success
PAYFAST_CANCEL_URL=http://localhost:3001/payment/cancel
PAYFAST_NOTIFY_URL=http://localhost:3001/api/payment/payfast/notify
```

### **3. Redux Store Setup**

```typescript
// lib/redux/store.ts
import { configureStore } from '@reduxjs/toolkit';
import { payFastReducer } from '@/modules/payments/payfast';

export const store = configureStore({
  reducer: {
    // ... other reducers
    payfast: payFastReducer,
  },
});
```

### **4. Basic Usage**

```typescript
import { PayFastCheckout, usePayFast } from '@/modules/payments/payfast';

function CheckoutPage() {
  const { createAndProcessPayment } = usePayFast();

  return (
    <PayFastCheckout
      orderId="ORD-123"
      amount={100.00}
      description="Test Purchase"
      customerEmail="<EMAIL>"
      customerName="John Doe"
      onSuccess={(result) => console.log('Payment successful:', result)}
      onError={(error) => console.error('Payment failed:', error)}
    />
  );
}
```

## 📁 **Module Structure**

```
modules/payments/payfast/
├── types/
│   └── index.ts              # TypeScript definitions
├── utils/
│   └── index.ts              # Utility functions
├── services/
│   └── PayFastService.ts     # Core service class
├── store/
│   └── payFastSlice.ts       # Redux slice
├── hooks/
│   └── usePayFast.ts         # React hooks
├── components/
│   ├── PayFastCheckout.tsx   # Simple checkout component
│   ├── PayFastPaymentForm.tsx # Full payment form
│   ├── PayFastStatus.tsx     # Payment status tracker
│   ├── PayFastHistory.tsx    # Payment history viewer
│   └── index.ts              # Component exports
├── index.ts                  # Main module export
└── README.md                 # This file
```

## 🔧 **Components**

### **PayFastCheckout**
Simple, elegant checkout component for quick payments.

```typescript
<PayFastCheckout
  orderId="ORD-123"
  amount={100.00}
  description="Product Purchase"
  customerEmail="<EMAIL>"
  customerName="John Doe"
  onSuccess={(result) => handleSuccess(result)}
  onError={(error) => handleError(error)}
  onCancel={() => handleCancel()}
/>
```

### **PayFastPaymentForm**
Comprehensive payment form with validation and recurring payment support.

```typescript
<PayFastPaymentForm
  orderId="ORD-123"
  amount={100.00}
  description="Subscription"
  customerEmail="<EMAIL>"
  customerName="John Doe"
  allowRecurring={true}
  showPaymentMethods={true}
  onSuccess={(result) => handleSuccess(result)}
  onError={(error) => handleError(error)}
/>
```

### **PayFastStatus**
Real-time payment status monitoring with polling.

```typescript
<PayFastStatus
  orderId="ORD-123"
  onStatusUpdate={(status) => handleStatusUpdate(status)}
  pollInterval={5000}
  maxAttempts={12}
/>
```

### **PayFastHistory**
Payment history viewer with filtering and export.

```typescript
<PayFastHistory
  userId="user-123"
  limit={10}
  showFilters={true}
  onPaymentSelect={(payment) => handlePaymentSelect(payment)}
/>
```

## 🎣 **Hooks**

### **usePayFast**
Main hook for PayFast operations.

```typescript
const {
  // State
  isLoading,
  currentPayment,
  paymentHistory,
  error,

  // Actions
  createPayment,
  createRecurringPayment,
  fetchPaymentHistory,
  checkPaymentStatus,
  processPayment,
  createAndProcessPayment,

  // Utilities
  getPaymentByOrderId,
  getSuccessfulPayments,
  getTotalAmountPaid,
  clearCurrentPayment
} = usePayFast();
```

### **usePayFastStatusPolling**
Automatic payment status polling.

```typescript
usePayFastStatusPolling('ORD-123', {
  enabled: true,
  interval: 5000,
  maxAttempts: 12,
  onStatusUpdate: (status) => console.log('Status:', status),
  onComplete: (status) => console.log('Final status:', status),
  onError: (error) => console.error('Polling error:', error)
});
```

### **usePayFastConfig**
Configuration management.

```typescript
const { config, isConfigured, isSandbox } = usePayFastConfig();
```

## 🛠 **Service Layer**

### **PayFastService**
Core service for PayFast operations.

```typescript
import { PayFastService } from '@/modules/payments/payfast';

const payfast = new PayFastService({
  merchantId: 'your_merchant_id',
  merchantKey: 'your_merchant_key',
  passphrase: 'your_passphrase',
  sandbox: true,
  returnUrl: 'https://yoursite.com/success',
  cancelUrl: 'https://yoursite.com/cancel',
  notifyUrl: 'https://yoursite.com/webhook'
});

// Create payment
const payment = await payfast.createPayment({
  orderId: 'ORD-123',
  amount: 100.00,
  description: 'Test Purchase',
  customerEmail: '<EMAIL>',
  customerName: 'John Doe'
});

// Verify webhook notification
const isValid = payfast.verifyNotification(webhookData);
```

## 🔧 **Utilities**

### **PayFastUtils**
Utility functions for validation, signature generation, etc.

```typescript
import { PayFastUtils } from '@/modules/payments/payfast';

// Generate signature
const signature = PayFastUtils.generateSignature(data, passphrase);

// Validate amount
const validation = PayFastUtils.validateAmount(100.00);

// Format amount
const formatted = PayFastUtils.formatAmount(100.123); // "100.12"

// Generate order ID
const orderId = PayFastUtils.generateOrderId('PF'); // "PF-1234567890-ABC123"
```

## 📊 **State Management**

### **Redux Actions**

```typescript
import { 
  createPayFastPayment,
  fetchPayFastPaymentHistory,
  checkPayFastPaymentStatus 
} from '@/modules/payments/payfast';

// Create payment
dispatch(createPayFastPayment({
  orderId: 'ORD-123',
  amount: 100.00,
  description: 'Test',
  customerEmail: '<EMAIL>',
  customerName: 'John Doe'
}));

// Fetch history
dispatch(fetchPayFastPaymentHistory('user-123'));

// Check status
dispatch(checkPayFastPaymentStatus('ORD-123'));
```

### **Selectors**

```typescript
import { 
  selectPayFastLoading,
  selectPayFastCurrentPayment,
  selectPayFastPaymentHistory,
  selectPayFastError 
} from '@/modules/payments/payfast';

const isLoading = useSelector(selectPayFastLoading);
const currentPayment = useSelector(selectPayFastCurrentPayment);
const history = useSelector(selectPayFastPaymentHistory);
const error = useSelector(selectPayFastError);
```

## 🔒 **Security Features**

- ✅ **Signature Verification** - All webhooks verified
- ✅ **Input Validation** - Comprehensive validation
- ✅ **Type Safety** - Full TypeScript coverage
- ✅ **Error Handling** - Graceful error management
- ✅ **Sanitization** - Input sanitization for PayFast

## 🎨 **Styling**

Components use Tailwind CSS and shadcn/ui components:
- Responsive design
- Dark mode support
- Consistent styling
- Accessible components
- Smooth animations with Framer Motion

## 🧪 **Testing**

```typescript
// Example test
import { PayFastUtils } from '@/modules/payments/payfast';

describe('PayFastUtils', () => {
  test('validates amount correctly', () => {
    const result = PayFastUtils.validateAmount(100.00);
    expect(result.isValid).toBe(true);
  });

  test('generates valid signature', () => {
    const signature = PayFastUtils.generateSignature(data, passphrase);
    expect(signature).toHaveLength(32);
  });
});
```

## 📈 **Performance**

- **Lazy Loading** - Components load on demand
- **Memoization** - Optimized re-renders
- **Efficient State** - Minimal state updates
- **Debounced Validation** - Smooth user experience

## 🔄 **Integration Examples**

### **Checkout Flow**
```typescript
function CheckoutFlow() {
  const [step, setStep] = useState('payment');
  
  return (
    <>
      {step === 'payment' && (
        <PayFastCheckout
          orderId={orderId}
          amount={total}
          description={description}
          customerEmail={user.email}
          customerName={user.name}
          onSuccess={() => setStep('success')}
          onError={() => setStep('error')}
        />
      )}
      {step === 'success' && <SuccessPage />}
      {step === 'error' && <ErrorPage />}
    </>
  );
}
```

### **Subscription Management**
```typescript
function SubscriptionPage() {
  return (
    <PayFastPaymentForm
      orderId={`SUB-${Date.now()}`}
      amount={29.99}
      description="Monthly Subscription"
      customerEmail={user.email}
      customerName={user.name}
      allowRecurring={true}
      onSuccess={handleSubscriptionSuccess}
    />
  );
}
```

## 📞 **Support**

- **Documentation**: See `/PAYMENT_INTEGRATION_GUIDE.md`
- **PayFast Docs**: https://developers.payfast.co.za/docs
- **PayFast Support**: https://www.payfast.co.za/help

## 🔄 **Version History**

- **v1.0.0** - Initial release with full PayFast integration
- Complete TypeScript support
- Redux state management
- React components
- Comprehensive utilities

---

This module provides everything needed for PayFast integration in StokvelGrocery while maintaining modularity and reusability.
