// Cash on Delivery Module - Server-side exports only
// This file should be used in API routes and server components

// Types
export * from './types';

// Services (Server-side only)
export { CODService } from './services/CODService';

// Utils (Server-side safe)
export { CODUtils } from './utils';

// Module Configuration (Server-side safe)
export const CODModule = {
  name: 'Cash on Delivery',
  version: '1.0.0',
  description: 'Complete Cash on Delivery payment module for StokvelGrocery',
  provider: 'StokvelGrocery',
  countries: ['South Africa'],
  currencies: ['ZAR'],
  features: {
    standardPayments: true,
    recurringPayments: false,
    refunds: false,
    webhooks: false,
    subscriptions: false,
    multiCurrency: false,
    mobileOptimized: true,
    groupPayments: true,
    deliveryTracking: true,
    phoneVerification: true,
    addressVerification: true,
    deliveryScheduling: true
  },
  paymentMethods: [
    'Cash on Delivery'
  ],
  fees: {
    deliveryFee: 'R50 (fixed) or percentage-based',
    paymentProcessing: 'Free'
  },
  limits: {
    minAmount: 50,
    maxAmount: 5000,
    currency: 'ZAR'
  },
  supportedAreas: [
    'Cape Town',
    'Johannesburg', 
    'Durban',
    'Pretoria',
    'Port Elizabeth'
  ],
  deliveryOptions: {
    estimatedDays: '2-5 business days',
    trackingAvailable: true,
    deliverySlots: true,
    specialInstructions: true
  }
};

// Module Utilities (Server-side safe)
export const CODModuleUtils = {
  /**
   * Get default COD configuration
   */
  getDefaultConfig: () => ({
    enabled: true,
    maxAmount: 5000,
    minAmount: 50,
    deliveryFee: 50,
    deliveryFeeType: 'fixed' as const,
    supportedAreas: ['Cape Town', 'Johannesburg', 'Durban', 'Pretoria'],
    estimatedDeliveryDays: 3,
    requiresPhoneVerification: true,
    requiresAddressVerification: true
  }),

  /**
   * Validate module configuration
   */
  validateConfig: (config: any) => {
    return CODUtils.validateConfig(config);
  },

  /**
   * Get module information
   */
  getModuleInfo: () => CODModule,

  /**
   * Check if module supports feature
   */
  supportsFeature: (feature: keyof typeof CODModule.features) => {
    return CODModule.features[feature];
  },

  /**
   * Get supported payment methods
   */
  getPaymentMethods: () => CODModule.paymentMethods,

  /**
   * Get fee structure
   */
  getFees: () => CODModule.fees,

  /**
   * Get payment limits
   */
  getLimits: () => CODModule.limits,

  /**
   * Generate module-specific order ID
   */
  generateOrderId: (prefix?: string) => {
    return CODUtils.generateTrackingNumber(prefix || 'COD');
  },

  /**
   * Get supported areas
   */
  getSupportedAreas: () => CODModule.supportedAreas,

  /**
   * Check if delivery is available in area
   */
  isDeliveryAvailable: (city: string, postalCode: string) => {
    return CODModule.supportedAreas.some(area => 
      city.toLowerCase().includes(area.toLowerCase()) ||
      postalCode.startsWith(area)
    );
  },

  /**
   * Calculate delivery estimate
   */
  getDeliveryEstimate: (address: any, amount: number) => {
    const config = {
      enabled: true,
      maxAmount: 5000,
      minAmount: 50,
      deliveryFee: 50,
      deliveryFeeType: 'fixed' as const,
      supportedAreas: CODModule.supportedAreas,
      estimatedDeliveryDays: 3,
      requiresPhoneVerification: true,
      requiresAddressVerification: true
    };

    return CODUtils.getDeliveryEstimate(address, amount, config);
  },

  /**
   * Format delivery address
   */
  formatAddress: (address: any) => {
    return CODUtils.formatAddress(address);
  },

  /**
   * Validate phone number
   */
  validatePhone: (phone: string) => {
    return CODUtils.validatePhoneNumber(phone);
  },

  /**
   * Format phone number
   */
  formatPhone: (phone: string) => {
    return CODUtils.formatPhoneNumber(phone);
  },

  /**
   * Get status display info
   */
  getStatusDisplay: (status: string) => {
    return CODUtils.getStatusDisplay(status);
  },

  /**
   * Check COD eligibility
   */
  checkEligibility: (amount: number, address: any) => {
    const config = {
      enabled: true,
      maxAmount: 5000,
      minAmount: 50,
      deliveryFee: 50,
      deliveryFeeType: 'fixed' as const,
      supportedAreas: CODModule.supportedAreas,
      estimatedDeliveryDays: 3,
      requiresPhoneVerification: true,
      requiresAddressVerification: true
    };

    return CODUtils.validateCODEligibility(amount, address, config);
  }
};

// Default export for server-side usage
export default {
  ...CODModule,
  utils: CODModuleUtils
};
